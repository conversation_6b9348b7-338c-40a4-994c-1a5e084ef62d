import { NextRequest, NextResponse } from 'next/server';
import Groq from 'groq-sdk';

export async function GET() {
  try {
    console.log('🔍 Test de connexion Groq...');
    
    // Vérifier la clé API
    const apiKey = process.env.GROQ_API_KEY;
    console.log('📋 Clé API configurée:', apiKey ? `${apiKey.substring(0, 10)}...` : 'NON');
    
    if (!apiKey) {
      return NextResponse.json({
        success: false,
        error: 'Aucune clé API Groq trouvée',
        details: 'GROQ_API_KEY non configurée dans .env'
      });
    }
    
    if (!apiKey.startsWith('gsk_')) {
      return NextResponse.json({
        success: false,
        error: 'Format de clé API incorrect',
        details: 'La clé doit commencer par "gsk_"'
      });
    }
    
    // Initialiser Groq
    console.log('🔧 Initialisation du client Groq...');
    const groq = new Groq({
      apiKey: api<PERSON><PERSON>,
    });
    
    // Test de connexion simple
    console.log('🧪 Test de connexion...');
    
    const completion = await groq.chat.completions.create({
      messages: [
        {
          role: "user",
          content: "Dis juste 'Test réussi' en français"
        }
      ],
      model: "llama-3.1-70b-versatile",
      max_tokens: 10,
      temperature: 0.1,
    });
    
    const response = completion.choices[0]?.message?.content;
    console.log('📨 Réponse de Groq:', response);
    
    if (response) {
      console.log('🎉 SUCCÈS: Connexion Groq fonctionnelle !');
      
      return NextResponse.json({
        success: true,
        message: 'Connexion Groq réussie',
        response: response,
        model: 'llama-3.1-70b-versatile',
        apiKeyFormat: 'Valide (gsk_...)',
        timestamp: new Date().toISOString()
      });
    } else {
      return NextResponse.json({
        success: false,
        error: 'Pas de réponse de Groq',
        details: 'L\'API a répondu mais sans contenu'
      });
    }
    
  } catch (error: any) {
    console.error('❌ Erreur Groq:', error);
    
    let errorDetails = {
      success: false,
      error: 'Erreur de connexion Groq',
      details: error.message,
      type: error.constructor.name,
      timestamp: new Date().toISOString()
    };
    
    if (error.status === 401) {
      errorDetails.details = 'Clé API invalide ou expirée';
      errorDetails.error = 'Authentification échouée';
    } else if (error.status === 429) {
      errorDetails.details = 'Limite de taux atteinte';
      errorDetails.error = 'Trop de requêtes';
    } else if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
      errorDetails.details = 'Problème de connexion réseau';
      errorDetails.error = 'Connexion réseau échouée';
    }
    
    return NextResponse.json(errorDetails, { status: 500 });
  }
}
