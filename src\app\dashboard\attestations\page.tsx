import { AppSidebarWrapper } from "@/components/app-sidebar-wrapper"
import { SiteHeader } from "@/components/site-header"
import { AttestationsManager } from "@/components/attestations-manager"
import {
  SidebarInset,
  SidebarProvider,
} from "@/components/ui/sidebar"
import { getAttestations, getDemandes } from "@/lib/data"

export default async function AttestationsPage() {
  const [attestations, demandes] = await Promise.all([
    getAttestations(),
    getDemandes(),
  ])
  return (
    <SidebarProvider
      style={
        {
          "--sidebar-width": "calc(var(--spacing) * 72)",
          "--header-height": "calc(var(--spacing) * 12)",
        } as React.CSSProperties
      }
    >
      <AppSidebarWrapper variant="inset" />
      <SidebarInset>
        <SiteHeader />
        <div className="flex flex-1 flex-col">
          <div className="@container/main flex flex-1 flex-col gap-2">
            <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
              <div className="px-4 lg:px-6">
                <div className="flex flex-col gap-4">
                  <div>
                    <h1 className="text-2xl font-bold">Gestion des Attestations</h1>
                    <p className="text-muted-foreground">
                      Générez automatiquement des attestations avec l'IA et gérez leur envoi
                    </p>
                  </div>
                  <AttestationsManager attestations={attestations} demandes={demandes} />
                </div>
              </div>
            </div>
          </div>
        </div>
      </SidebarInset>
    </SidebarProvider>
  )
}
