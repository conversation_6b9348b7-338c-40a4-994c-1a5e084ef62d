import { AppSidebarWrapper } from "@/components/app-sidebar-wrapper"
import { SiteHeader } from "@/components/site-header"
import { CongesManager } from "@/components/conges-manager"
import {
  SidebarInset,
  SidebarProvider,
} from "@/components/ui/sidebar"
import { getDemandes, getUsers } from "@/lib/data"

export default async function CongesPage() {
  const [demandes, users] = await Promise.all([
    getDemandes(),
    getUsers(),
  ])

  // Filtrer uniquement les demandes de congés
  const demandesConges = demandes.filter(demande => demande.type === 'CONGE')

  return (
    <SidebarProvider
      style={
        {
          "--sidebar-width": "calc(var(--spacing) * 72)",
          "--header-height": "calc(var(--spacing) * 12)",
        } as React.CSSProperties
      }
    >
      <AppSidebarWrapper variant="inset" />
      <SidebarInset>
        <SiteHeader />
        <div className="flex flex-1 flex-col">
          <div className="@container/main flex flex-1 flex-col gap-2">
            <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
              <div className="px-4 lg:px-6">
                <div className="flex flex-col gap-4">
                  <div>
                    <h1 className="text-2xl font-bold">Gestion des Congés</h1>
                    <p className="text-muted-foreground">
                      Gérez toutes les demandes de congés : validation, refus, suppression
                    </p>
                  </div>
                  <CongesManager demandes={demandesConges} users={users} />
                </div>
              </div>
            </div>
          </div>
        </div>
      </SidebarInset>
    </SidebarProvider>
  )
}
