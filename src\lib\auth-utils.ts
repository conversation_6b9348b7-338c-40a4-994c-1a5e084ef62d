import { auth } from "@/lib/auth"
import { redirect } from "next/navigation"

// Types de rôles
export type Role = "EMPLOYE" | "MANAGER" | "RH" | "ADMIN"

// Obtenir la session utilisateur actuelle
export async function getCurrentUser() {
  try {
    const session = await auth()
    return session?.user || null
  } catch (error) {
    console.error("Erreur lors de la récupération de la session:", error)
    return null
  }
}

// Middleware pour protéger les routes
export async function requireAuth() {
  const user = await getCurrentUser()
  if (!user) {
    redirect("/auth/signin")
  }
  return user
}

// Vérifier si un utilisateur a un rôle spécifique
export function hasRole(userRole: string, requiredRole: Role): boolean {
  return userRole === requiredRole || userRole === "ADMIN"
}

// Obtenir le nom d'affichage d'un rôle
export function getRoleDisplayName(role: string): string {
  const roleNames: Record<string, string> = {
    EMPLOYE: "Employé",
    MANAGER: "Manager",
    RH: "Ressources Humaines",
    ADMIN: "Administrateur",
  }
  return roleNames[role] || role
}

// Définition des permissions par rôle
const PERMISSIONS = {
  EMPLOYE: [
    "view_own_profile",
    "create_demande",
    "view_own_demandes",
  ],
  MANAGER: [
    "view_own_profile",
    "create_demande",
    "view_own_demandes",
    "view_team_profiles",
    "validate_team_demandes",
  ],
  RH: [
    "view_own_profile",
    "create_demande",
    "view_own_demandes",
    "view_all_profiles",
    "manage_all_demandes",
    "generate_attestations",
    "manage_conges",
  ],
  ADMIN: [
    "view_own_profile",
    "create_demande",
    "view_own_demandes",
    "view_all_profiles",
    "manage_all_demandes",
    "generate_attestations",
    "manage_conges",
    "manage_users",
    "manage_roles",
    "system_settings",
    "view_statistics",
  ],
}

// Vérifier si un utilisateur a une permission spécifique
export function hasPermission(userRole: string, permission: string): boolean {
  if (!userRole || !permission) return false

  // Les admins ont toutes les permissions
  if (userRole === "ADMIN") return true

  // Vérifier si le rôle a la permission spécifique
  const rolePermissions = PERMISSIONS[userRole as keyof typeof PERMISSIONS] || []
  return rolePermissions.includes(permission)
}
