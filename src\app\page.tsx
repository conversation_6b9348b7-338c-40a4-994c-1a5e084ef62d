import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { getCurrentUser } from "@/lib/auth-utils";
import { redirect } from "next/navigation";

export default async function Home() {
  // Si l'utilisateur est déjà connecté, rediriger vers le dashboard
  const user = await getCurrentUser();
  if (user) {
    redirect("/dashboard");
  }
  return (
    <div className="flex min-h-screen flex-col items-center justify-center p-8">
      <div className="mx-auto max-w-md space-y-6">
        <div className="space-y-2 text-center">
          <h1 className="text-3xl font-bold">Système de Gestion RH</h1>
          <p className="text-muted-foreground">
            Connectez-vous pour accéder à votre espace de travail
          </p>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Connexion</CardTitle>
            <CardDescription>
              Accédez à votre espace de gestion RH
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="grid grid-cols-2 gap-3">
              <Link href="/auth/signin">
                <Button className="w-full">
                  Se connecter
                </Button>
              </Link>
              <Link href="/auth/signup">
                <Button variant="outline" className="w-full">
                  S'inscrire
                </Button>
              </Link>
            </div>
            <div className="text-center space-y-2">
              <p className="text-xs text-muted-foreground">
                Créez un compte ou connectez-vous avec vos identifiants
              </p>
              <p className="text-xs text-muted-foreground">
                Comptes de test disponibles dans la page de connexion
              </p>
            </div>
          </CardContent>
        </Card>

        <div className="text-center text-sm text-muted-foreground">
          <p>Créé avec Next.js, Tailwind CSS et shadcn/ui</p>
        </div>
      </div>
    </div>
  );
}
