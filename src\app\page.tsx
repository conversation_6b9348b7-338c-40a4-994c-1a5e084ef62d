"use client"

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { getSession } from 'next-auth/react';
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import {
  FileText, Calendar, Shield, Users, CheckCircle, Clock, ArrowRight,
  Sparkles, Star, Play, ChevronDown, Award, TrendingUp, Zap,
  MessageSquare, BarChart3, Globe, Smartphone, Lock, Bot,
  CheckCircle2, ArrowUpRight, Menu, X, Briefcase, Target,
  Rocket, Heart, Coffee, Lightbulb, Database, Cloud
} from "lucide-react";

export default function HomePage() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [activeTestimonial, setActiveTestimonial] = useState(0);
  const router = useRouter();

  // Vérifier si l'utilisateur est déjà connecté
  useEffect(() => {
    const checkSession = async () => {
      const session = await getSession();
      if (session) {
        router.push('/dashboard');
      }
    };
    checkSession();
  }, [router]);

  const testimonials = [
    {
      name: "Sarah Dubois",
      role: "Directrice RH, TechCorp",
      content: "RH Manager IA a révolutionné notre gestion RH. L'automatisation des attestations nous fait gagner 80% de temps et nos employés adorent la simplicité.",
      rating: 5,
      avatar: "SD",
      company: "TechCorp",
      image: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face"
    },
    {
      name: "Marc Lefebvre",
      role: "Responsable RH, InnovatePlus",
      content: "L'IA intégrée génère des documents parfaits en quelques secondes. Notre productivité a explosé et la satisfaction employés est au maximum.",
      rating: 5,
      avatar: "ML",
      company: "InnovatePlus",
      image: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face"
    },
    {
      name: "Julie Martin",
      role: "Gestionnaire RH, StartupTech",
      content: "Interface magnifique, fonctionnalités puissantes et support exceptionnel. RH Manager est devenu indispensable à notre croissance.",
      rating: 5,
      avatar: "JM",
      company: "StartupTech",
      image: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face"
    }
  ];

  const stats = [
    { number: "15K+", label: "Employés actifs", icon: Users, color: "from-blue-500 to-cyan-500" },
    { number: "800+", label: "Entreprises", icon: Briefcase, color: "from-purple-500 to-pink-500" },
    { number: "99.8%", label: "Satisfaction", icon: Heart, color: "from-green-500 to-emerald-500" },
    { number: "< 100ms", label: "Temps de réponse", icon: Zap, color: "from-orange-500 to-red-500" }
  ];

  const features = [
    {
      icon: Bot,
      title: "IA Générative Avancée",
      description: "Intelligence artificielle de pointe qui génère automatiquement des attestations personnalisées et conformes en quelques secondes.",
      details: ["Génération IA instantanée", "Personnalisation intelligente", "Conformité automatique"],
      color: "from-blue-500 to-purple-600",
      gradient: "bg-gradient-to-br from-blue-50 to-purple-50"
    },
    {
      icon: Calendar,
      title: "Gestion Intelligente des Congés",
      description: "Système de gestion des congés avec prédictions IA, planification optimisée et workflows automatisés pour tous types de demandes.",
      details: ["Prédictions IA", "Planification optimisée", "Workflows automatisés"],
      color: "from-green-500 to-emerald-600",
      gradient: "bg-gradient-to-br from-green-50 to-emerald-50"
    },
    {
      icon: Shield,
      title: "Sécurité Militaire",
      description: "Protection maximale avec chiffrement quantique, authentification biométrique et conformité RGPD + ISO 27001 certifiée.",
      details: ["Chiffrement quantique", "Biométrie avancée", "Conformité totale"],
      color: "from-red-500 to-pink-600",
      gradient: "bg-gradient-to-br from-red-50 to-pink-50"
    },
    {
      icon: BarChart3,
      title: "Analytics Prédictifs",
      description: "Tableaux de bord intelligents avec analyses prédictives, insights automatiques et recommandations IA pour optimiser vos RH.",
      details: ["Analyses prédictives", "Insights automatiques", "Recommandations IA"],
      color: "from-orange-500 to-yellow-600",
      gradient: "bg-gradient-to-br from-orange-50 to-yellow-50"
    },
    {
      icon: Cloud,
      title: "Cloud Hybride Intelligent",
      description: "Infrastructure cloud hybride avec synchronisation temps réel, sauvegarde automatique et disponibilité 99.99%.",
      details: ["Sync temps réel", "Sauvegarde auto", "99.99% uptime"],
      color: "from-cyan-500 to-blue-600",
      gradient: "bg-gradient-to-br from-cyan-50 to-blue-50"
    },
    {
      icon: Lightbulb,
      title: "Innovation Continue",
      description: "Plateforme évolutive avec mises à jour automatiques, nouvelles fonctionnalités IA et intégrations sans limite.",
      details: ["Mises à jour auto", "Nouvelles fonctions IA", "Intégrations illimitées"],
      color: "from-purple-500 to-indigo-600",
      gradient: "bg-gradient-to-br from-purple-50 to-indigo-50"
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
      {/* Header moderne avec glassmorphism */}
      <header className="fixed top-0 w-full z-50 bg-white/80 backdrop-blur-xl border-b border-white/20 shadow-lg shadow-blue-500/5">
        <div className="container mx-auto px-6 py-4">
          <div className="flex justify-between items-center">
            <div className="flex items-center space-x-3">
              <div className="relative">
                <div className="w-12 h-12 bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-600 rounded-2xl flex items-center justify-center shadow-lg shadow-blue-500/25">
                  <Bot className="w-7 h-7 text-white" />
                </div>
                <div className="absolute -top-1 -right-1 w-4 h-4 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full animate-pulse"></div>
              </div>
              <div>
                <span className="text-2xl font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-purple-800 bg-clip-text text-transparent">
                  RH Manager IA
                </span>
                <div className="text-xs text-gray-500 font-medium">Powered by AI</div>
              </div>
            </div>

            {/* Navigation desktop avec effets */}
            <nav className="hidden md:flex items-center space-x-8">
              <a href="#features" className="relative text-gray-600 hover:text-blue-600 transition-all duration-300 group">
                Fonctionnalités
                <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-blue-600 to-purple-600 group-hover:w-full transition-all duration-300"></span>
              </a>
              <a href="#testimonials" className="relative text-gray-600 hover:text-blue-600 transition-all duration-300 group">
                Témoignages
                <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-blue-600 to-purple-600 group-hover:w-full transition-all duration-300"></span>
              </a>
              <a href="#pricing" className="relative text-gray-600 hover:text-blue-600 transition-all duration-300 group">
                Tarifs
                <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-blue-600 to-purple-600 group-hover:w-full transition-all duration-300"></span>
              </a>
            </nav>

            <div className="flex items-center space-x-4">
              <Link href="/auth/signin" className="hidden md:inline-flex items-center space-x-2 text-gray-600 hover:text-blue-600 transition-all duration-300 font-medium">
                <Shield className="w-4 h-4" />
                <span>Se connecter</span>
              </Link>
              <Link href="/auth/signup" className="relative overflow-hidden bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 text-white px-6 py-3 rounded-full hover:shadow-lg hover:shadow-blue-500/25 transition-all duration-300 transform hover:scale-105 font-semibold">
                <span className="relative z-10 flex items-center space-x-2">
                  <Rocket className="w-4 h-4" />
                  <span>Commencer</span>
                </span>
                <div className="absolute inset-0 bg-gradient-to-r from-blue-700 via-purple-700 to-indigo-700 opacity-0 hover:opacity-100 transition-opacity duration-300"></div>
              </Link>
            </div>
          </div>
        </div>
      </header>

      {/* Section Hero révolutionnaire */}
      <section className="relative pt-32 pb-20 overflow-hidden">
        {/* Arrière-plan animé */}
        <div className="absolute inset-0 bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
          <div className="absolute top-20 left-10 w-72 h-72 bg-blue-300 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob"></div>
          <div className="absolute top-40 right-10 w-72 h-72 bg-purple-300 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-2000"></div>
          <div className="absolute -bottom-8 left-20 w-72 h-72 bg-pink-300 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-4000"></div>
        </div>

        <div className="relative container mx-auto px-6">
          <div className="text-center max-w-6xl mx-auto">
            {/* Badge IA avec animation */}
            <div className="inline-flex items-center space-x-3 bg-white/80 backdrop-blur-sm border border-white/20 rounded-full px-6 py-3 mb-8 shadow-lg shadow-blue-500/10">
              <div className="relative">
                <Bot className="w-6 h-6 text-blue-600" />
                <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-400 rounded-full animate-ping"></div>
              </div>
              <span className="text-sm font-semibold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                🚀 IA Générative Avancée • Groq Lightning Fast
              </span>
            </div>

            {/* Titre principal avec effet de frappe */}
            <h1 className="text-5xl md:text-7xl lg:text-8xl font-bold mb-8 leading-tight">
              <span className="block text-gray-900 mb-2">L'avenir des</span>
              <span className="block bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 bg-clip-text text-transparent animate-gradient-x">
                Ressources Humaines
              </span>
              <span className="block text-gray-700 text-3xl md:text-4xl lg:text-5xl mt-4 font-medium">
                commence maintenant
              </span>
            </h1>

            {/* Description avec animation */}
            <p className="text-xl md:text-2xl text-gray-600 mb-12 max-w-4xl mx-auto leading-relaxed">
              Révolutionnez votre gestion RH avec l'<span className="font-semibold text-blue-600">intelligence artificielle</span> la plus avancée.
              Génération automatique d'attestations, analytics prédictifs et expérience utilisateur exceptionnelle.
            </p>

            {/* Boutons CTA avec effets avancés */}
            <div className="flex flex-col sm:flex-row gap-6 justify-center mb-16">
              <Link href="/auth/signup" className="group relative overflow-hidden bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 text-white px-10 py-5 rounded-2xl text-lg font-bold shadow-2xl shadow-blue-500/25 hover:shadow-blue-500/40 transition-all duration-500 transform hover:scale-105">
                <span className="relative z-10 flex items-center justify-center space-x-3">
                  <Rocket className="w-6 h-6" />
                  <span>Démarrer gratuitement</span>
                  <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" />
                </span>
                <div className="absolute inset-0 bg-gradient-to-r from-blue-700 via-purple-700 to-indigo-700 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <div className="absolute inset-0 bg-white/20 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left"></div>
              </Link>

              <Link href="/auth/signin" className="group flex items-center justify-center space-x-3 bg-white/80 backdrop-blur-sm border-2 border-gray-200 text-gray-700 px-10 py-5 rounded-2xl text-lg font-semibold hover:border-blue-300 hover:bg-white transition-all duration-300 shadow-lg hover:shadow-xl">
                <Shield className="w-5 h-5 text-blue-600" />
                <span>Se connecter</span>
                <Play className="w-4 h-4 group-hover:scale-110 transition-transform duration-300" />
              </Link>
            </div>

            {/* Stats avec animations et icônes */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-5xl mx-auto">
              {stats.map((stat, index) => (
                <Card key={index} className="group bg-white/60 backdrop-blur-sm border-white/20 hover:bg-white/80 transition-all duration-300 hover:scale-105 hover:shadow-lg">
                  <CardContent className="p-6 text-center">
                    <div className={`w-12 h-12 bg-gradient-to-r ${stat.color} rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300`}>
                      <stat.icon className="w-6 h-6 text-white" />
                    </div>
                    <div className="text-3xl md:text-4xl font-bold text-gray-900 mb-2">{stat.number}</div>
                    <div className="text-gray-600 font-medium">{stat.label}</div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Section Fonctionnalités révolutionnaires */}
      <section id="features" className="py-24 bg-gradient-to-br from-gray-50 to-blue-50">
        <div className="container mx-auto px-6">
          <div className="text-center mb-20">
            <div className="inline-flex items-center space-x-2 bg-blue-100 rounded-full px-4 py-2 mb-6">
              <Lightbulb className="w-5 h-5 text-blue-600" />
              <span className="text-sm font-semibold text-blue-800">Innovation IA</span>
            </div>
            <h2 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
              Fonctionnalités qui
              <span className="block bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                révolutionnent les RH
              </span>
            </h2>
            <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
              Découvrez une suite complète d'outils alimentés par l'IA pour transformer
              votre gestion des ressources humaines en expérience exceptionnelle.
            </p>
          </div>

          <div className="grid lg:grid-cols-3 gap-8 max-w-7xl mx-auto">
            {features.map((feature, index) => (
              <Card key={index} className={`group relative overflow-hidden border-0 shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-3 ${feature.gradient}`}>
                <div className="absolute inset-0 bg-white/60 backdrop-blur-sm"></div>
                <CardContent className="relative z-10 p-8">
                  <div className={`w-16 h-16 bg-gradient-to-r ${feature.color} rounded-3xl flex items-center justify-center mb-6 group-hover:scale-110 group-hover:rotate-3 transition-all duration-300 shadow-lg`}>
                    <feature.icon className="w-8 h-8 text-white" />
                  </div>

                  <h3 className="text-2xl font-bold text-gray-900 mb-4 group-hover:text-blue-600 transition-colors duration-300">
                    {feature.title}
                  </h3>

                  <p className="text-gray-600 mb-6 leading-relaxed text-lg">
                    {feature.description}
                  </p>

                  <div className="space-y-3">
                    {feature.details.map((detail, idx) => (
                      <div key={idx} className="flex items-center space-x-3 group/item">
                        <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center group-hover/item:bg-green-200 transition-colors duration-200">
                          <CheckCircle className="w-4 h-4 text-green-600" />
                        </div>
                        <span className="text-gray-700 font-medium group-hover/item:text-gray-900 transition-colors duration-200">
                          {detail}
                        </span>
                      </div>
                    ))}
                  </div>

                  <div className="mt-6 pt-6 border-t border-gray-200/50">
                    <Button className={`w-full bg-gradient-to-r ${feature.color} hover:shadow-lg transition-all duration-300 text-white font-semibold`}>
                      Découvrir
                      <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform duration-300" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Section Témoignages */}
      <section id="testimonials" className="py-20 bg-gradient-to-br from-gray-50 to-gray-100">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Ils nous font confiance
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Découvrez comment RH Manager transforme le quotidien des équipes RH
              dans des entreprises de toutes tailles.
            </p>
          </div>

          <div className="max-w-4xl mx-auto">
            <div className="bg-white rounded-3xl p-8 md:p-12 shadow-xl">
              <div className="text-center mb-8">
                <div className="flex justify-center mb-4">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="w-6 h-6 text-yellow-400 fill-current" />
                  ))}
                </div>
                <blockquote className="text-xl md:text-2xl text-gray-700 leading-relaxed mb-8">
                  "{testimonials[activeTestimonial].content}"
                </blockquote>
                <div className="flex items-center justify-center space-x-4">
                  <div className="w-12 h-12 bg-gradient-to-br from-blue-600 to-purple-600 rounded-full flex items-center justify-center text-white font-bold">
                    {testimonials[activeTestimonial].avatar}
                  </div>
                  <div className="text-left">
                    <div className="font-semibold text-gray-900">{testimonials[activeTestimonial].name}</div>
                    <div className="text-gray-600">{testimonials[activeTestimonial].role}</div>
                  </div>
                </div>
              </div>

              <div className="flex justify-center space-x-3">
                {testimonials.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => setActiveTestimonial(index)}
                    className={`w-3 h-3 rounded-full transition-all duration-300 ${
                      index === activeTestimonial
                        ? 'bg-blue-600 w-8'
                        : 'bg-gray-300 hover:bg-gray-400'
                    }`}
                  />
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Section CTA finale */}
      <section className="py-20 bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-700">
        <div className="container mx-auto px-6 text-center">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Prêt à révolutionner votre RH ?
            </h2>
            <p className="text-xl text-blue-100 mb-12 max-w-2xl mx-auto">
              Rejoignez plus de 500 entreprises qui ont déjà transformé
              leur gestion RH avec notre plateforme intelligente.
            </p>

            <div className="flex flex-col sm:flex-row gap-6 justify-center mb-12">
              <Link href="/auth/signup" className="bg-white text-blue-600 px-8 py-4 rounded-full text-lg font-semibold hover:bg-gray-100 transition-all duration-300 transform hover:scale-105 shadow-lg">
                Essai gratuit 14 jours
              </Link>
              <Link href="/auth/signin" className="border-2 border-white text-white px-8 py-4 rounded-full text-lg font-semibold hover:bg-white hover:text-blue-600 transition-all duration-300">
                Se connecter
              </Link>
            </div>

            <div className="flex flex-wrap justify-center items-center gap-8 text-blue-100">
              <div className="flex items-center">
                <CheckCircle2 className="w-5 h-5 mr-2" />
                Sans engagement
              </div>
              <div className="flex items-center">
                <CheckCircle2 className="w-5 h-5 mr-2" />
                Support 24/7
              </div>
              <div className="flex items-center">
                <CheckCircle2 className="w-5 h-5 mr-2" />
                Migration gratuite
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Footer moderne et parfait */}
      <footer className="relative bg-gradient-to-br from-gray-900 via-gray-800 to-black text-white py-20 overflow-hidden">
        {/* Arrière-plan décoratif */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-10 left-10 w-32 h-32 bg-blue-500 rounded-full filter blur-3xl"></div>
          <div className="absolute bottom-10 right-10 w-32 h-32 bg-purple-500 rounded-full filter blur-3xl"></div>
        </div>

        <div className="relative container mx-auto px-6">
          {/* Section principale */}
          <div className="grid lg:grid-cols-5 gap-12 mb-16">
            {/* Branding */}
            <div className="lg:col-span-2">
              <div className="flex items-center space-x-3 mb-6">
                <div className="relative">
                  <div className="w-12 h-12 bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-600 rounded-2xl flex items-center justify-center shadow-lg">
                    <Bot className="w-7 h-7 text-white" />
                  </div>
                  <div className="absolute -top-1 -right-1 w-4 h-4 bg-green-400 rounded-full animate-pulse"></div>
                </div>
                <div>
                  <span className="text-2xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
                    RH Manager IA
                  </span>
                  <div className="text-xs text-gray-400 font-medium">Powered by AI</div>
                </div>
              </div>
              <p className="text-gray-300 leading-relaxed mb-6 text-lg">
                La révolution RH commence ici. Transformez votre gestion des ressources humaines
                avec l'intelligence artificielle la plus avancée du marché.
              </p>

              {/* Stats rapides */}
              <div className="grid grid-cols-3 gap-4 mb-6">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-400">15K+</div>
                  <div className="text-xs text-gray-400">Utilisateurs</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-400">800+</div>
                  <div className="text-xs text-gray-400">Entreprises</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-400">99.8%</div>
                  <div className="text-xs text-gray-400">Satisfaction</div>
                </div>
              </div>

              {/* Boutons d'action */}
              <div className="flex space-x-4">
                <Link href="/auth/signup" className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-3 rounded-xl font-semibold hover:shadow-lg hover:shadow-blue-500/25 transition-all duration-300 transform hover:scale-105">
                  Commencer gratuitement
                </Link>
                <Link href="/auth/signin" className="border border-gray-600 text-gray-300 px-6 py-3 rounded-xl font-semibold hover:border-gray-400 hover:text-white transition-all duration-300">
                  Se connecter
                </Link>
              </div>
            </div>

            {/* Liens organisés */}
            <div>
              <h3 className="font-bold text-white mb-6 text-lg">Produit</h3>
              <ul className="space-y-3">
                <li><a href="#features" className="text-gray-400 hover:text-blue-400 transition-colors duration-300 flex items-center group">
                  <ArrowRight className="w-4 h-4 mr-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                  Fonctionnalités IA
                </a></li>
                <li><a href="#testimonials" className="text-gray-400 hover:text-blue-400 transition-colors duration-300 flex items-center group">
                  <ArrowRight className="w-4 h-4 mr-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                  Témoignages
                </a></li>
                <li><a href="#" className="text-gray-400 hover:text-blue-400 transition-colors duration-300 flex items-center group">
                  <ArrowRight className="w-4 h-4 mr-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                  API & Intégrations
                </a></li>
                <li><a href="#" className="text-gray-400 hover:text-blue-400 transition-colors duration-300 flex items-center group">
                  <ArrowRight className="w-4 h-4 mr-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                  Sécurité
                </a></li>
              </ul>
            </div>

            <div>
              <h3 className="font-bold text-white mb-6 text-lg">Solutions</h3>
              <ul className="space-y-3">
                <li><a href="#" className="text-gray-400 hover:text-purple-400 transition-colors duration-300 flex items-center group">
                  <ArrowRight className="w-4 h-4 mr-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                  Attestations IA
                </a></li>
                <li><a href="#" className="text-gray-400 hover:text-purple-400 transition-colors duration-300 flex items-center group">
                  <ArrowRight className="w-4 h-4 mr-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                  Gestion congés
                </a></li>
                <li><a href="#" className="text-gray-400 hover:text-purple-400 transition-colors duration-300 flex items-center group">
                  <ArrowRight className="w-4 h-4 mr-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                  Analytics RH
                </a></li>
                <li><a href="#" className="text-gray-400 hover:text-purple-400 transition-colors duration-300 flex items-center group">
                  <ArrowRight className="w-4 h-4 mr-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                  Gestion rôles
                </a></li>
              </ul>
            </div>

            <div>
              <h3 className="font-bold text-white mb-6 text-lg">Support</h3>
              <ul className="space-y-3">
                <li><a href="#" className="text-gray-400 hover:text-green-400 transition-colors duration-300 flex items-center group">
                  <ArrowRight className="w-4 h-4 mr-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                  Documentation
                </a></li>
                <li><a href="#" className="text-gray-400 hover:text-green-400 transition-colors duration-300 flex items-center group">
                  <ArrowRight className="w-4 h-4 mr-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                  Centre d'aide
                </a></li>
                <li><a href="#" className="text-gray-400 hover:text-green-400 transition-colors duration-300 flex items-center group">
                  <ArrowRight className="w-4 h-4 mr-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                  Support 24/7
                </a></li>
                <li><a href="#" className="text-gray-400 hover:text-green-400 transition-colors duration-300 flex items-center group">
                  <ArrowRight className="w-4 h-4 mr-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                  Statut système
                </a></li>
              </ul>
            </div>
          </div>

          {/* Séparateur avec style */}
          <div className="border-t border-gradient-to-r from-transparent via-gray-700 to-transparent mb-8"></div>

          {/* Footer bottom */}
          <div className="flex flex-col lg:flex-row justify-between items-center space-y-6 lg:space-y-0">
            <div className="flex flex-col sm:flex-row items-center space-y-2 sm:space-y-0 sm:space-x-6">
              <p className="text-gray-400 text-sm">
                © 2025 RH Manager IA. Tous droits réservés.
              </p>
              <div className="flex items-center space-x-4 text-xs text-gray-500">
                <span className="flex items-center">
                  <Shield className="w-3 h-3 mr-1" />
                  RGPD Conforme
                </span>
                <span className="flex items-center">
                  <Lock className="w-3 h-3 mr-1" />
                  ISO 27001
                </span>
                <span className="flex items-center">
                  <CheckCircle className="w-3 h-3 mr-1" />
                  SOC 2 Type II
                </span>
              </div>
            </div>

            <div className="flex space-x-6">
              <a href="#" className="text-gray-400 hover:text-white transition-colors text-sm hover:underline">
                Politique de confidentialité
              </a>
              <a href="#" className="text-gray-400 hover:text-white transition-colors text-sm hover:underline">
                Conditions d'utilisation
              </a>
              <a href="#" className="text-gray-400 hover:text-white transition-colors text-sm hover:underline">
                Mentions légales
              </a>
            </div>
          </div>

          {/* Badge technologique */}
          <div className="text-center mt-8 pt-8 border-t border-gray-800">
            <p className="text-xs text-gray-500">
              Développé avec ❤️ en France • Next.js 15 • Tailwind CSS • Prisma • IA Groq • TypeScript
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}