"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"

export default function TestAuthPage() {
  const [email, setEmail] = useState("<EMAIL>")
  const [password, setPassword] = useState("admin123")
  const [isLoading, setIsLoading] = useState(false)
  const [result, setResult] = useState<any>(null)

  const testAuth = async () => {
    setIsLoading(true)
    setResult(null)

    try {
      // Test direct de notre API
      const response = await fetch('/api/test-auth', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email,
          password
        })
      })

      const data = await response.json()
      console.log('Response:', response.status, data)

      setResult({
        status: response.status,
        data: data,
        success: response.ok
      })

    } catch (error) {
      console.error('Erreur:', error)
      setResult({
        error: error.message,
        success: false
      })
    } finally {
      setIsLoading(false)
    }
  }

  const testSignIn = async () => {
    setIsLoading(true)
    setResult(null)

    try {
      // Test avec signIn de next-auth
      const { signIn } = await import("next-auth/react")

      const result = await signIn("credentials", {
        email,
        password,
        redirect: false,
      })

      console.log('SignIn result:', result)
      setResult(result)

    } catch (error) {
      console.error('Erreur signIn:', error)
      setResult({
        error: error.message,
        success: false
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 p-4">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle>Test d'authentification</CardTitle>
          <CardDescription>
            Diagnostic des problèmes de connexion
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="email">Email</Label>
            <Input
              id="email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="password">Mot de passe</Label>
            <Input
              id="password"
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
            />
          </div>

          <div className="space-y-2">
            <Button
              onClick={testAuth}
              disabled={isLoading}
              className="w-full"
            >
              Test API Direct
            </Button>

            <Button
              onClick={testSignIn}
              disabled={isLoading}
              variant="outline"
              className="w-full"
            >
              Test SignIn NextAuth
            </Button>
          </div>

          {result && (
            <Alert className={result.success ? "border-green-500" : "border-red-500"}>
              <AlertDescription>
                <pre className="text-xs overflow-auto">
                  {JSON.stringify(result, null, 2)}
                </pre>
              </AlertDescription>
            </Alert>
          )}

          <div className="text-xs text-gray-600 space-y-1">
            <p><strong>Comptes de test :</strong></p>
            <p><EMAIL> / admin123</p>
            <p><EMAIL> / rh123</p>
            <p><EMAIL> / manager123</p>
            <p><EMAIL> / employe123</p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
