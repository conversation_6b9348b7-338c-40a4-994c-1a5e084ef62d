import NextAuth from "next-auth"
import Cred<PERSON><PERSON><PERSON><PERSON>ider from "next-auth/providers/credentials"
import bcrypt from "bcryptjs"
import { prisma } from "@/lib/prisma"

export const { handlers, auth, signIn, signOut } = NextAuth({
  debug: process.env.NODE_ENV === "development",
  session: {
    strategy: "jwt",
    maxAge: 30 * 24 * 60 * 60, // 30 jours
  },
  providers: [
    CredentialsProvider({
      id: "credentials",
      name: "Credentials",
      credentials: {
        email: {
          label: "Email",
          type: "email",
          placeholder: "<EMAIL>"
        },
        password: {
          label: "Mot de passe",
          type: "password",
          placeholder: "••••••••"
        }
      },
      async authorize(credentials) {
        console.log("🔐 Tentative de connexion pour:", credentials?.email)

        if (!credentials?.email || !credentials?.password) {
          console.log("❌ Credentials manquantes")
          return null
        }

        try {
          const user = await prisma.user.findUnique({
            where: {
              email: credentials.email.toLowerCase().trim()
            }
          })

          if (!user) {
            console.log("❌ Utilisateur non trouvé:", credentials.email)
            return null
          }

          if (!user.password) {
            console.log("❌ Pas de mot de passe défini pour:", credentials.email)
            return null
          }

          const isPasswordValid = await bcrypt.compare(
            credentials.password,
            user.password
          )

          if (!isPasswordValid) {
            console.log("❌ Mot de passe incorrect pour:", credentials.email)
            return null
          }

          console.log("✅ Connexion réussie pour:", credentials.email)

          // Construire le nom d'affichage
          let displayName = user.name
          if (!displayName && (user.prenom || user.nom)) {
            displayName = `${user.prenom || ''} ${user.nom || ''}`.trim()
          }
          if (!displayName) {
            displayName = user.email.split('@')[0] // Utiliser la partie avant @ de l'email
          }

          return {
            id: user.id,
            email: user.email,
            name: displayName,
            role: user.role,
            image: user.avatar || null,
          }
        } catch (error) {
          console.error("❌ Erreur d'authentification:", error)
          return null
        }
      }
    })
  ],
  callbacks: {
    async jwt({ token, user, account }) {
      if (user) {
        token.role = user.role
        token.userId = user.id
        token.email = user.email
        token.name = user.name
      }
      return token
    },
    async session({ session, token }) {
      if (token) {
        session.user.id = token.userId as string
        session.user.role = token.role as string
        session.user.email = token.email as string
        session.user.name = token.name as string
      }
      return session
    },
    async redirect({ url, baseUrl }) {
      // Redirection après connexion
      if (url.startsWith("/")) return `${baseUrl}${url}`
      else if (new URL(url).origin === baseUrl) return url
      return `${baseUrl}/dashboard`
    }
  },
  pages: {
    signIn: "/auth/signin",
    error: "/auth/error",
  },
  events: {
    async signIn({ user, account, profile }) {
      console.log("🎉 Utilisateur connecté:", user.email)
    },
    async signOut({ session, token }) {
      console.log("👋 Utilisateur déconnecté")
    }
  }
})

// Types pour étendre NextAuth
declare module "next-auth" {
  interface Session {
    user: {
      id: string
      email: string
      name?: string | null
      role: string
    }
  }

  interface User {
    role: string
  }
}

declare module "next-auth/jwt" {
  interface JWT {
    role: string
    userId: string
  }
}
