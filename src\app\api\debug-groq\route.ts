import { NextResponse } from 'next/server';

export async function GET() {
  try {
    // Diagnostic de base
    const diagnostic = {
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV,
      groqApiKey: {
        configured: !!process.env.GROQ_API_KEY,
        format: process.env.GROQ_API_KEY ? 
          (process.env.GROQ_API_KEY.startsWith('gsk_') ? 'Correct (gsk_)' : 'Incorrect (ne commence pas par gsk_)') :
          'Non configurée',
        length: process.env.GROQ_API_KEY ? process.env.GROQ_API_KEY.length : 0,
        preview: process.env.GROQ_API_KEY ? `${process.env.GROQ_API_KEY.substring(0, 10)}...` : 'N/A'
      }
    };

    // Test d'import du module Groq
    let groqImportTest = {
      success: false,
      error: null as string | null
    };

    try {
      const Groq = (await import('groq-sdk')).default;
      groqImportTest.success = true;
      
      // Test d'initialisation
      if (process.env.GROQ_API_KEY) {
        const groq = new Groq({
          apiKey: process.env.GROQ_API_KEY,
        });
        
        // Test de connexion simple
        const completion = await groq.chat.completions.create({
          messages: [{ role: "user", content: "Hello" }],
          model: "llama-3.1-70b-versatile",
          max_tokens: 5,
        });
        
        return NextResponse.json({
          ...diagnostic,
          groqImport: groqImportTest,
          connectionTest: {
            success: true,
            response: completion.choices[0]?.message?.content,
            model: 'llama-3.1-70b-versatile'
          }
        });
      }
      
    } catch (error: any) {
      groqImportTest.error = error.message;
      
      return NextResponse.json({
        ...diagnostic,
        groqImport: groqImportTest,
        connectionTest: {
          success: false,
          error: error.message,
          type: error.constructor.name,
          status: error.status || 'N/A'
        }
      });
    }

    return NextResponse.json({
      ...diagnostic,
      groqImport: groqImportTest,
      connectionTest: {
        success: false,
        error: 'Clé API non configurée'
      }
    });

  } catch (error: any) {
    return NextResponse.json({
      success: false,
      error: 'Erreur de diagnostic',
      details: error.message,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
