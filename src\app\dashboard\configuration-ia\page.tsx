import { AppSidebarWrapper } from "@/components/app-sidebar-wrapper"
import { SiteHeader } from "@/components/site-header"
import { ConfigurationIA } from "@/components/configuration-ia"
import {
  SidebarInset,
  SidebarProvider,
} from "@/components/ui/sidebar"

export default async function ConfigurationIAPage() {
  return (
    <SidebarProvider
      style={
        {
          "--sidebar-width": "calc(var(--spacing) * 72)",
          "--header-height": "calc(var(--spacing) * 12)",
        } as React.CSSProperties
      }
    >
      <AppSidebarWrapper variant="inset" />
      <SidebarInset>
        <SiteHeader />
        <div className="flex flex-1 flex-col">
          <div className="@container/main flex flex-1 flex-col gap-2">
            <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
              <div className="px-4 lg:px-6">
                <div className="flex flex-col gap-4">
                  <div>
                    <h1 className="text-2xl font-bold">Configuration IA</h1>
                    <p className="text-muted-foreground">
                      Configurez et testez l'intégration IA gratuite Groq pour la génération d'attestations
                    </p>
                  </div>
                  <ConfigurationIA />
                </div>
              </div>
            </div>
          </div>
        </div>
      </SidebarInset>
    </SidebarProvider>
  )
}
