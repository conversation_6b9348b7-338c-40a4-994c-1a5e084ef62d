import { auth } from "@/lib/auth"
import { NextResponse } from "next/server"

// Routes publiques qui ne nécessitent pas d'authentification
const publicRoutes = [
  "/",
  "/auth/signin",
]

export default auth((req) => {
  const { nextUrl } = req
  const isLoggedIn = !!req.auth

  // Vérifier si la route est publique
  const isPublicRoute = publicRoutes.includes(nextUrl.pathname)

  // Si l'utilisateur n'est pas connecté et essaie d'accéder à une route protégée
  if (!isLoggedIn && !isPublicRoute) {
    return NextResponse.redirect(new URL("/auth/signin", nextUrl))
  }

  // Si l'utilisateur est connecté et essaie d'accéder à la page de connexion
  if (isLoggedIn && nextUrl.pathname === "/auth/signin") {
    return NextResponse.redirect(new URL("/dashboard", nextUrl))
  }

  // Protection spéciale pour la gestion des rôles (ADMIN seulement)
  if (isLoggedIn && nextUrl.pathname === "/dashboard/gestion-roles") {
    if (req.auth?.user?.role !== "ADMIN") {
      return NextResponse.redirect(new URL("/dashboard", nextUrl))
    }
  }

  return NextResponse.next()
})

export const config = {
  matcher: [
    "/((?!api/auth|_next/static|_next/image|favicon.ico).*)",
  ],
}
