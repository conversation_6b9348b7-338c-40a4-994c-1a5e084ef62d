import { prisma } from './prisma'

export async function getDemandes() {
  try {
    const demandes = await prisma.demande.findMany({
      include: {
        user: true,
        attestation: true,
      },
      orderBy: {
        createdAt: 'desc',
      },
    })

    return demandes
  } catch (error) {
    console.error('Erreur lors de la récupération des demandes:', error)
    return []
  }
}

export async function getUsers() {
  try {
    const users = await prisma.user.findMany({
      include: {
        demandes: true,
      },
      orderBy: {
        createdAt: 'desc',
      },
    })

    return users
  } catch (error) {
    console.error('Erreur lors de la récupération des utilisateurs:', error)
    return []
  }
}

export async function getAttestations() {
  try {
    const attestations = await prisma.attestation.findMany({
      include: {
        demande: {
          include: {
            user: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    })

    return attestations
  } catch (error) {
    console.error('Erreur lors de la récupération des attestations:', error)
    return []
  }
}

export async function getNotifications() {
  try {
    const notifications = await prisma.notification.findMany({
      include: {
        user: true,
        demande: {
          include: {
            user: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    })

    return notifications
  } catch (error) {
    console.error('Erreur lors de la récupération des notifications:', error)
    return []
  }
}

export async function getStatistics() {
  try {
    const totalDemandes = await prisma.demande.count()
    const demandesEnAttente = await prisma.demande.count({
      where: { status: 'pending' }
    })
    const demandesValidees = await prisma.demande.count({
      where: { status: 'approved' }
    })
    const demandesRejetees = await prisma.demande.count({
      where: { status: 'rejected' }
    })
    const demandesEnCours = await prisma.demande.count({
      where: { status: 'in_progress' }
    })

    const totalUsers = await prisma.user.count()
    const totalAttestations = await prisma.attestation.count()

    const tauxTraitement = totalDemandes > 0
      ? Math.round(((demandesValidees + demandesRejetees) / totalDemandes) * 100)
      : 0

    return {
      totalDemandes,
      demandesEnAttente,
      demandesValidees,
      demandesRejetees,
      demandesEnCours,
      totalUsers,
      totalAttestations,
      tauxTraitement,
    }
  } catch (error) {
    console.error('Erreur lors du calcul des statistiques:', error)
    return {
      totalDemandes: 0,
      demandesEnAttente: 0,
      demandesValidees: 0,
      demandesRejetees: 0,
      demandesEnCours: 0,
      totalUsers: 0,
      totalAttestations: 0,
      tauxTraitement: 0,
    }
  }
}
