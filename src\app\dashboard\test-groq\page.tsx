"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { AppSidebar } from "@/components/app-sidebar"
import { SiteHeader } from "@/components/site-header"
import { SidebarInset, SidebarProvider } from "@/components/ui/sidebar"
import { TestTube, CheckCircle, XCircle, AlertCircle, Bot } from "lucide-react"

export default function TestGroqPage() {
  const [isLoading, setIsLoading] = useState(false)
  const [result, setResult] = useState<any>(null)

  const testGroqConnection = async () => {
    setIsLoading(true)
    setResult(null)

    try {
      const response = await fetch('/api/test-groq')
      const data = await response.json()
      
      setResult({
        ...data,
        httpStatus: response.status,
        httpOk: response.ok
      })
    } catch (error: any) {
      setResult({
        success: false,
        error: 'Erreur de requête',
        details: error.message,
        httpStatus: 'Network Error'
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <SidebarProvider>
      <AppSidebar variant="inset" />
      <SidebarInset>
        <SiteHeader />
        <div className="flex flex-1 flex-col">
          <div className="@container/main flex flex-1 flex-col gap-2">
            <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
              <div className="px-4 lg:px-6">
                <div className="flex flex-col gap-4">
                  <div>
                    <h1 className="text-2xl font-bold">Test de connexion Groq</h1>
                    <p className="text-muted-foreground">
                      Diagnostic de la connexion à l'API Groq
                    </p>
                  </div>

                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Bot className="h-5 w-5" />
                        Test de l'API Groq
                      </CardTitle>
                      <CardDescription>
                        Testez votre connexion à l'API Groq pour diagnostiquer les problèmes
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <Button 
                        onClick={testGroqConnection}
                        disabled={isLoading}
                        className="w-full"
                      >
                        <TestTube className="mr-2 h-4 w-4" />
                        {isLoading ? 'Test en cours...' : 'Tester la connexion Groq'}
                      </Button>

                      {result && (
                        <Alert className={result.success ? "border-green-200 bg-green-50" : "border-red-200 bg-red-50"}>
                          <div className="flex items-start gap-2">
                            {result.success ? (
                              <CheckCircle className="h-4 w-4 text-green-600 mt-0.5" />
                            ) : (
                              <XCircle className="h-4 w-4 text-red-600 mt-0.5" />
                            )}
                            <div className="flex-1">
                              <AlertDescription>
                                <div className="space-y-2">
                                  <div className="flex items-center gap-2">
                                    <strong>
                                      {result.success ? 'Connexion réussie !' : 'Connexion échouée'}
                                    </strong>
                                    <Badge variant={result.success ? "default" : "destructive"}>
                                      HTTP {result.httpStatus}
                                    </Badge>
                                  </div>
                                  
                                  {result.success && (
                                    <div className="space-y-1 text-sm">
                                      <p><strong>Réponse IA :</strong> {result.response}</p>
                                      <p><strong>Modèle :</strong> {result.model}</p>
                                      <p><strong>Clé API :</strong> {result.apiKeyFormat}</p>
                                      <p><strong>Timestamp :</strong> {result.timestamp}</p>
                                    </div>
                                  )}
                                  
                                  {!result.success && (
                                    <div className="space-y-1 text-sm">
                                      <p><strong>Erreur :</strong> {result.error}</p>
                                      <p><strong>Détails :</strong> {result.details}</p>
                                      {result.type && <p><strong>Type :</strong> {result.type}</p>}
                                    </div>
                                  )}
                                </div>
                              </AlertDescription>
                            </div>
                          </div>
                        </Alert>
                      )}

                      <Alert>
                        <AlertCircle className="h-4 w-4" />
                        <AlertDescription>
                          <strong>Solutions si la connexion échoue :</strong>
                          <ul className="list-disc list-inside mt-2 space-y-1 text-sm">
                            <li><strong>Clé invalide :</strong> Vérifiez votre clé sur console.groq.com</li>
                            <li><strong>Format incorrect :</strong> La clé doit commencer par "gsk_"</li>
                            <li><strong>Réseau :</strong> Vérifiez votre connexion internet</li>
                            <li><strong>Firewall :</strong> Autorisez les connexions vers api.groq.com</li>
                            <li><strong>Limite :</strong> Attendez quelques minutes si trop de requêtes</li>
                          </ul>
                        </AlertDescription>
                      </Alert>

                      <div className="text-xs text-muted-foreground">
                        <p><strong>Configuration actuelle :</strong></p>
                        <p>• Clé API : {process.env.GROQ_API_KEY ? 'Configurée' : 'Non configurée'}</p>
                        <p>• Modèle : llama-3.1-70b-versatile</p>
                        <p>• Endpoint : https://api.groq.com</p>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </div>
          </div>
        </div>
      </SidebarInset>
    </SidebarProvider>
  )
}
