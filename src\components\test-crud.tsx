"use client"

import * as React from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { 
  createDemande, 
  updateDemandeStatus, 
  deleteDemande, 
  createUser,
  updateUser,
  createAttestation,
  getStatisticsAction
} from "@/lib/actions"
import { toast } from "sonner"
import { 
  Plus, 
  Edit, 
  Trash2, 
  Check, 
  X, 
  RefreshCw,
  Database,
  Users,
  FileText,
  BarChart3
} from "lucide-react"

interface TestCrudComponentProps {
  demandes: any[]
  users: any[]
  statistics: any
}

export function TestCrudComponent({ demandes, users, statistics }: TestCrudComponentProps) {
  const [isLoading, setIsLoading] = React.useState(false)
  const [currentStats, setCurrentStats] = React.useState(statistics)

  // Fonction pour rafraîchir les statistiques
  const refreshStats = async () => {
    setIsLoading(true)
    try {
      const result = await getStatisticsAction()
      if (result.success) {
        setCurrentStats(result.data)
        toast.success('Statistiques mises à jour')
      }
    } catch (error) {
      toast.error('Erreur lors de la mise à jour des statistiques')
    } finally {
      setIsLoading(false)
    }
  }

  // Test CREATE - Créer une demande de test
  const testCreateDemande = async () => {
    if (users.length === 0) {
      toast.error('Aucun utilisateur disponible')
      return
    }

    setIsLoading(true)
    try {
      const result = await createDemande({
        type: 'CONGE',
        message: 'Demande de test créée automatiquement',
        userId: users[0].id,
      })
      
      if (result.success) {
        toast.success('✅ CREATE: Demande créée avec succès')
        await refreshStats()
      } else {
        toast.error('❌ CREATE: ' + result.error)
      }
    } catch (error) {
      toast.error('❌ CREATE: Erreur lors de la création')
    } finally {
      setIsLoading(false)
    }
  }

  // Test UPDATE - Approuver une demande en attente
  const testUpdateDemande = async () => {
    const pendingDemande = demandes.find(d => d.status === 'pending')
    if (!pendingDemande) {
      toast.error('Aucune demande en attente trouvée')
      return
    }

    setIsLoading(true)
    try {
      const result = await updateDemandeStatus(pendingDemande.id, 'approved')
      if (result.success) {
        toast.success('✅ UPDATE: Demande approuvée avec succès')
        await refreshStats()
      } else {
        toast.error('❌ UPDATE: ' + result.error)
      }
    } catch (error) {
      toast.error('❌ UPDATE: Erreur lors de la mise à jour')
    } finally {
      setIsLoading(false)
    }
  }

  // Test DELETE - Supprimer une demande
  const testDeleteDemande = async () => {
    if (demandes.length === 0) {
      toast.error('Aucune demande à supprimer')
      return
    }

    const demandeToDelete = demandes[demandes.length - 1] // Supprimer la dernière
    setIsLoading(true)
    try {
      const result = await deleteDemande(demandeToDelete.id)
      if (result.success) {
        toast.success('✅ DELETE: Demande supprimée avec succès')
        await refreshStats()
      } else {
        toast.error('❌ DELETE: ' + result.error)
      }
    } catch (error) {
      toast.error('❌ DELETE: Erreur lors de la suppression')
    } finally {
      setIsLoading(false)
    }
  }

  // Test CREATE USER
  const testCreateUser = async () => {
    setIsLoading(true)
    try {
      const result = await createUser({
        email: `test.user.${Date.now()}@entreprise.com`,
        name: 'Utilisateur Test',
        nom: 'Test',
        prenom: 'Utilisateur',
        poste: 'Testeur',
        departement: 'IT',
        role: 'user',
      })
      
      if (result.success) {
        toast.success('✅ CREATE USER: Utilisateur créé avec succès')
        await refreshStats()
      } else {
        toast.error('❌ CREATE USER: ' + result.error)
      }
    } catch (error) {
      toast.error('❌ CREATE USER: Erreur lors de la création')
    } finally {
      setIsLoading(false)
    }
  }

  // Test CREATE ATTESTATION
  const testCreateAttestation = async () => {
    const attestationDemande = demandes.find(d => d.type === 'ATTESTATION' && !d.attestation)
    if (!attestationDemande) {
      toast.error('Aucune demande d\'attestation sans attestation trouvée')
      return
    }

    setIsLoading(true)
    try {
      const result = await createAttestation({
        contenu: `Attestation de test générée automatiquement le ${new Date().toLocaleDateString('fr-FR')} pour ${attestationDemande.user?.name || 'Utilisateur inconnu'}.`,
        demandeId: attestationDemande.id,
        genereParIA: false,
      })
      
      if (result.success) {
        toast.success('✅ CREATE ATTESTATION: Attestation créée avec succès')
        await refreshStats()
      } else {
        toast.error('❌ CREATE ATTESTATION: ' + result.error)
      }
    } catch (error) {
      toast.error('❌ CREATE ATTESTATION: Erreur lors de la création')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* Statistiques actuelles */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Statistiques en temps réel
              </CardTitle>
              <CardDescription>
                Données actuelles de la base de données
              </CardDescription>
            </div>
            <Button variant="outline" onClick={refreshStats} disabled={isLoading}>
              <RefreshCw className={`mr-2 h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
              Actualiser
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{currentStats.totalDemandes}</div>
              <div className="text-sm text-muted-foreground">Total demandes</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">{currentStats.demandesEnAttente}</div>
              <div className="text-sm text-muted-foreground">En attente</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{currentStats.demandesValidees}</div>
              <div className="text-sm text-muted-foreground">Validées</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">{currentStats.totalUsers}</div>
              <div className="text-sm text-muted-foreground">Utilisateurs</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Tests CRUD */}
      <Tabs defaultValue="demandes" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="demandes">Demandes</TabsTrigger>
          <TabsTrigger value="users">Utilisateurs</TabsTrigger>
          <TabsTrigger value="attestations">Attestations</TabsTrigger>
          <TabsTrigger value="read">Lecture</TabsTrigger>
        </TabsList>
        
        <TabsContent value="demandes">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Database className="h-5 w-5" />
                Tests CRUD - Demandes
              </CardTitle>
              <CardDescription>
                Testez les opérations Create, Update, Delete sur les demandes
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-3">
                <Button 
                  onClick={testCreateDemande} 
                  disabled={isLoading}
                  className="h-20 flex-col gap-2"
                >
                  <Plus className="h-6 w-6" />
                  <span>CREATE</span>
                  <span className="text-xs">Créer demande</span>
                </Button>
                
                <Button 
                  onClick={testUpdateDemande} 
                  disabled={isLoading}
                  variant="outline"
                  className="h-20 flex-col gap-2"
                >
                  <Edit className="h-6 w-6" />
                  <span>UPDATE</span>
                  <span className="text-xs">Approuver demande</span>
                </Button>
                
                <Button 
                  onClick={testDeleteDemande} 
                  disabled={isLoading}
                  variant="destructive"
                  className="h-20 flex-col gap-2"
                >
                  <Trash2 className="h-6 w-6" />
                  <span>DELETE</span>
                  <span className="text-xs">Supprimer demande</span>
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="users">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Tests CRUD - Utilisateurs
              </CardTitle>
              <CardDescription>
                Testez les opérations sur les utilisateurs
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button 
                onClick={testCreateUser} 
                disabled={isLoading}
                className="h-20 flex-col gap-2 w-full md:w-auto"
              >
                <Plus className="h-6 w-6" />
                <span>CREATE USER</span>
                <span className="text-xs">Créer utilisateur</span>
              </Button>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="attestations">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Tests CRUD - Attestations
              </CardTitle>
              <CardDescription>
                Testez la création d'attestations
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button 
                onClick={testCreateAttestation} 
                disabled={isLoading}
                className="h-20 flex-col gap-2 w-full md:w-auto"
              >
                <FileText className="h-6 w-6" />
                <span>CREATE ATTESTATION</span>
                <span className="text-xs">Générer attestation</span>
              </Button>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="read">
          <Card>
            <CardHeader>
              <CardTitle>Données actuelles (READ)</CardTitle>
              <CardDescription>
                Visualisation des données en base
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h4 className="font-medium mb-2">Demandes ({demandes.length})</h4>
                  <div className="flex flex-wrap gap-2">
                    {demandes.slice(0, 5).map((demande) => (
                      <Badge key={demande.id} variant="outline">
                        {demande.type} - {demande.status}
                      </Badge>
                    ))}
                    {demandes.length > 5 && (
                      <Badge variant="secondary">+{demandes.length - 5} autres</Badge>
                    )}
                  </div>
                </div>
                
                <div>
                  <h4 className="font-medium mb-2">Utilisateurs ({users.length})</h4>
                  <div className="flex flex-wrap gap-2">
                    {users.slice(0, 5).map((user) => (
                      <Badge key={user.id} variant="outline">
                        {user.name}
                      </Badge>
                    ))}
                    {users.length > 5 && (
                      <Badge variant="secondary">+{users.length - 5} autres</Badge>
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
