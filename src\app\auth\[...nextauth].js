import NextAuth from "next-auth";
import GoogleProvider from "next-auth/providers/google";

export default NextAuth({
  // Configure one or more authentication providers
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET,
    }),
    // tu peux ajouter d’autres providers ici
  ],
  secret: process.env.NEXTAUTH_SECRET,
  session: {
    strategy: "jwt", // ou "database" si tu utilises une base de données
  },
  callbacks: {
    async jwt({ token, account, profile }) {
      // Ajoute des infos au token JWT à la connexion
      if (account) {
        token.accessToken = account.access_token;
        token.id = profile.sub;
      }
      return token;
    },
    async session({ session, token, user }) {
      // Ajoute des infos supplémentaires à la session
      session.user.id = token.id;
      session.accessToken = token.accessToken;
      return session;
    },
  },
});
