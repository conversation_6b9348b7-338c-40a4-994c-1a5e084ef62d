import OpenAI from 'openai';

// Configuration OpenAI
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

// Types pour les attestations
export type TypeAttestation = 'travail' | 'stage';

export interface DonneesEmploye {
  nom: string;
  prenom: string;
  poste?: string;
  departement?: string;
  dateEmbauche?: Date;
  dateDebutStage?: Date;
  dateFinStage?: Date;
  etablissement?: string;
  motif?: string;
}

// Modèles de prompts pour chaque type d'attestation
const PROMPTS = {
  travail: `Tu es un assistant RH professionnel. Génère une attestation de travail officielle en français.

Informations de l'employé:
- Nom: {nom}
- Prénom: {prenom}
- Poste: {poste}
- Département: {departement}
- Date d'embauche: {dateEmbauche}
- Motif de la demande: {motif}

Génère une attestation de travail professionnelle et formelle qui certifie que cette personne travaille dans l'entreprise. L'attestation doit:
- Être rédigée à la première personne du singulier ("Je soussigné...")
- Mentionner le poste et le département
- Indiquer la date d'embauche si disponible
- Être datée d'aujourd'hui
- Inclure une formule de politesse finale
- Faire environ 150-200 mots
- Être formatée de manière professionnelle

Ne pas inclure de signature ou d'en-tête d'entreprise spécifique.`,

  stage: `Tu es un assistant RH professionnel. Génère une attestation de stage officielle en français.

Informations du stagiaire:
- Nom: {nom}
- Prénom: {prenom}
- Poste/Mission: {poste}
- Département: {departement}
- Date de début: {dateDebutStage}
- Date de fin: {dateFinStage}
- Établissement d'origine: {etablissement}
- Motif de la demande: {motif}

Génère une attestation de stage professionnelle et formelle qui certifie que cette personne a effectué un stage dans l'entreprise. L'attestation doit:
- Être rédigée à la première personne du singulier ("Je soussigné...")
- Mentionner la mission/poste du stage
- Indiquer les dates de début et fin du stage
- Mentionner l'établissement d'origine si disponible
- Être datée d'aujourd'hui
- Inclure une formule de politesse finale
- Faire environ 150-200 mots
- Être formatée de manière professionnelle

Ne pas inclure de signature ou d'en-tête d'entreprise spécifique.`
};

// Fonction pour remplacer les variables dans le prompt
function remplaceVariables(template: string, donnees: DonneesEmploye): string {
  return template
    .replace('{nom}', donnees.nom || 'Non spécifié')
    .replace('{prenom}', donnees.prenom || 'Non spécifié')
    .replace('{poste}', donnees.poste || 'Non spécifié')
    .replace('{departement}', donnees.departement || 'Non spécifié')
    .replace('{dateEmbauche}', donnees.dateEmbauche ? donnees.dateEmbauche.toLocaleDateString('fr-FR') : 'Non spécifiée')
    .replace('{dateDebutStage}', donnees.dateDebutStage ? donnees.dateDebutStage.toLocaleDateString('fr-FR') : 'Non spécifiée')
    .replace('{dateFinStage}', donnees.dateFinStage ? donnees.dateFinStage.toLocaleDateString('fr-FR') : 'Non spécifiée')
    .replace('{etablissement}', donnees.etablissement || 'Non spécifié')
    .replace('{motif}', donnees.motif || 'servir et valoir ce que de droit');
}

// Fonction principale pour générer une attestation avec OpenAI
export async function genererAttestationAvecIA(
  type: TypeAttestation,
  donnees: DonneesEmploye
): Promise<string> {
  try {
    // Vérifier que la clé API est configurée
    if (!process.env.OPENAI_API_KEY) {
      throw new Error('Clé API OpenAI non configurée');
    }

    // Sélectionner le prompt approprié
    const promptTemplate = PROMPTS[type];
    const prompt = remplaceVariables(promptTemplate, donnees);

    // Appel à l'API OpenAI
    const completion = await openai.chat.completions.create({
      model: "gpt-3.5-turbo", // Modèle gratuit avec les crédits OpenAI
      messages: [
        {
          role: "system",
          content: "Tu es un assistant RH professionnel spécialisé dans la rédaction d'attestations officielles en français."
        },
        {
          role: "user",
          content: prompt
        }
      ],
      max_tokens: 500,
      temperature: 0.3, // Peu de créativité pour un document officiel
    });

    const contenu = completion.choices[0]?.message?.content;
    
    if (!contenu) {
      throw new Error('Aucun contenu généré par l\'IA');
    }

    return contenu.trim();
  } catch (error) {
    console.error('Erreur lors de la génération avec OpenAI:', error);
    
    // Fallback vers la génération manuelle en cas d'erreur
    return genererAttestationManuelle(type, donnees);
  }
}

// Fonction de fallback pour générer une attestation sans IA
function genererAttestationManuelle(type: TypeAttestation, donnees: DonneesEmploye): string {
  const dateActuelle = new Date().toLocaleDateString('fr-FR');
  
  if (type === 'travail') {
    return `Je soussigné, Directeur des Ressources Humaines, certifie par la présente que :

${donnees.prenom} ${donnees.nom}
${donnees.poste ? `Poste occupé : ${donnees.poste}` : ''}
${donnees.departement ? `Département : ${donnees.departement}` : ''}

est employé(e) dans notre entreprise ${donnees.dateEmbauche ? `depuis le ${donnees.dateEmbauche.toLocaleDateString('fr-FR')}` : ''} ${donnees.poste ? `en qualité de ${donnees.poste}` : ''}.

Cette attestation est délivrée à sa demande pour ${donnees.motif || 'servir et valoir ce que de droit'}.

Fait le ${dateActuelle}

Directeur des Ressources Humaines`;
  } else {
    return `Je soussigné, Directeur des Ressources Humaines, certifie par la présente que :

${donnees.prenom} ${donnees.nom}
${donnees.etablissement ? `Étudiant(e) à ${donnees.etablissement}` : ''}

a effectué un stage dans notre entreprise ${donnees.dateDebutStage && donnees.dateFinStage ? `du ${donnees.dateDebutStage.toLocaleDateString('fr-FR')} au ${donnees.dateFinStage.toLocaleDateString('fr-FR')}` : ''} ${donnees.poste ? `en qualité de ${donnees.poste}` : ''}.
${donnees.departement ? `Au sein du département ${donnees.departement}.` : ''}

Cette attestation est délivrée à sa demande pour ${donnees.motif || 'servir et valoir ce que de droit'}.

Fait le ${dateActuelle}

Directeur des Ressources Humaines`;
  }
}

// Fonction pour tester la connexion OpenAI
export async function testerConnexionOpenAI(): Promise<boolean> {
  try {
    if (!process.env.OPENAI_API_KEY) {
      return false;
    }

    const completion = await openai.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: [{ role: "user", content: "Test de connexion" }],
      max_tokens: 5,
    });

    return !!completion.choices[0]?.message?.content;
  } catch (error) {
    console.error('Erreur de connexion OpenAI:', error);
    return false;
  }
}
