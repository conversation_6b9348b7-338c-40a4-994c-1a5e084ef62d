"use client"

import * as React from "react"
import {
  IconCamera,
  IconChartBar,
  IconDashboard,
  IconFileAi,
  IconFileDescription,
  IconFileWord,
  IconFolder,
  IconHelp,
  IconInnerShadowTop,
  IconListDetails,
  IconReport,
  IconSearch,
  IconSettings,
  IconShield,
  IconUsers,
} from "@tabler/icons-react"

import { NavMain } from "@/components/nav-main"
import { NavSecondary } from "@/components/nav-secondary"
import { NavUser } from "@/components/nav-user"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar"

const data = {
  user: {
    name: "Admin RH",
    email: "<EMAIL>",
    avatar: "/avatars/admin.jpg",
  },
  navMain: [
    {
      title: "Dashboard",
      url: "/dashboard",
      icon: IconDashboard,
    },
    {
      title: "Demandes",
      url: "/dashboard/demandes",
      icon: IconListDetails,
    },
    {
      title: "Attestations",
      url: "/dashboard/attestations",
      icon: IconFileDescription,
    },
    {
      title: "Employés",
      url: "/dashboard/employes",
      icon: IconUsers,
    },
    {
      title: "Statistiques",
      url: "/dashboard/statistiques",
      icon: IconChartBar,
    },
    {
      title: "Gestion des Rôles",
      url: "/dashboard/gestion-roles",
      icon: IconShield,
    },
  ],
  navClouds: [
    {
      title: "Demandes",
      icon: IconListDetails,
      isActive: true,
      url: "/dashboard/demandes",
      items: [
        {
          title: "Congés",
          url: "/dashboard/demandes?type=conge",
        },
        {
          title: "Matériel",
          url: "/dashboard/demandes?type=materiel",
        },
        {
          title: "Attestations",
          url: "/dashboard/demandes?type=attestation",
        },
        {
          title: "En attente",
          url: "/dashboard/demandes?statut=en_attente",
        },
        {
          title: "Validées",
          url: "/dashboard/demandes?statut=validee",
        },
      ],
    },
    {
      title: "Attestations",
      icon: IconFileDescription,
      url: "/dashboard/attestations",
      items: [
        {
          title: "Générer",
          url: "/dashboard/attestations/generer",
        },
        {
          title: "En cours",
          url: "/dashboard/attestations?statut=en_cours",
        },
        {
          title: "Envoyées",
          url: "/dashboard/attestations?statut=envoyee",
        },
      ],
    },
    {
      title: "Départements",
      icon: IconFolder,
      url: "/dashboard/departements",
      items: [
        {
          title: "IT",
          url: "/dashboard/demandes?departement=it",
        },
        {
          title: "RH",
          url: "/dashboard/demandes?departement=rh",
        },
        {
          title: "Finance",
          url: "/dashboard/demandes?departement=finance",
        },
        {
          title: "Marketing",
          url: "/dashboard/demandes?departement=marketing",
        },
      ],
    },
  ],
  navSecondary: [
    {
      title: "Paramètres",
      url: "/dashboard/parametres",
      icon: IconSettings,
    },
    {
      title: "Notifications",
      url: "/dashboard/notifications",
      icon: IconHelp,
    },
    {
      title: "Recherche",
      url: "/dashboard/recherche",
      icon: IconSearch,
    },
  ],
}

interface AppSidebarProps extends React.ComponentProps<typeof Sidebar> {
  user?: {
    name: string
    email: string
    avatar: string
    role?: string
  }
  navMain?: any[]
  navSecondary?: any[]
}

export function AppSidebar({ user, navMain, navSecondary, ...props }: AppSidebarProps) {
  const userData = user || data.user
  const navMainItems = navMain || data.navMain
  const navSecondaryItems = navSecondary || data.navSecondary

  return (
    <Sidebar collapsible="offcanvas" {...props}>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton
              asChild
              className="data-[slot=sidebar-menu-button]:!p-1.5"
            >
              <a href="/dashboard">
                <IconInnerShadowTop className="!size-5" />
                <span className="text-base font-semibold">RH Manager</span>
              </a>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={navMainItems} />
        <NavSecondary items={navSecondaryItems} className="mt-auto" />
      </SidebarContent>
      <SidebarFooter>
        <NavUser user={userData} />
      </SidebarFooter>
    </Sidebar>
  )
}
