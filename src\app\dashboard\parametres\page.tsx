import { AppSidebarWrapper } from "@/components/app-sidebar-wrapper"
import { SiteHeader } from "@/components/site-header"
import {
  SidebarInset,
  SidebarProvider,
} from "@/components/ui/sidebar"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Settings, Mail, Building, Shield } from "lucide-react"

export default function ParametresPage() {
  return (
    <SidebarProvider
      style={
        {
          "--sidebar-width": "calc(var(--spacing) * 72)",
          "--header-height": "calc(var(--spacing) * 12)",
        } as React.CSSProperties
      }
    >
      <AppSidebarWrapper variant="inset" />
      <SidebarInset>
        <SiteHeader />
        <div className="flex flex-1 flex-col">
          <div className="@container/main flex flex-1 flex-col gap-2">
            <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
              <div className="px-4 lg:px-6">
                <div className="flex flex-col gap-4">
                  <div>
                    <h1 className="text-2xl font-bold">Paramètres</h1>
                    <p className="text-muted-foreground">
                      Configurez les paramètres du système de gestion RH
                    </p>
                  </div>

                  <Tabs defaultValue="entreprise" className="w-full">
                    <TabsList className="grid w-full grid-cols-4">
                      <TabsTrigger value="entreprise">Entreprise</TabsTrigger>
                      <TabsTrigger value="workflow">Workflow</TabsTrigger>
                      <TabsTrigger value="email">Email</TabsTrigger>
                      <TabsTrigger value="securite">Sécurité</TabsTrigger>
                    </TabsList>

                    <TabsContent value="entreprise">
                      <Card>
                        <CardHeader>
                          <CardTitle className="flex items-center gap-2">
                            <Building className="h-5 w-5" />
                            Informations de l'entreprise
                          </CardTitle>
                          <CardDescription>
                            Configurez les informations générales de votre entreprise
                          </CardDescription>
                        </CardHeader>
                        <CardContent>
                          <p>Configuration des informations entreprise...</p>
                        </CardContent>
                      </Card>
                    </TabsContent>

                    <TabsContent value="workflow">
                      <Card>
                        <CardHeader>
                          <CardTitle className="flex items-center gap-2">
                            <Settings className="h-5 w-5" />
                            Paramètres de workflow
                          </CardTitle>
                          <CardDescription>
                            Configurez les règles de validation et de traitement
                          </CardDescription>
                        </CardHeader>
                        <CardContent>
                          <p>Configuration du workflow...</p>
                        </CardContent>
                      </Card>
                    </TabsContent>

                    <TabsContent value="email">
                      <Card>
                        <CardHeader>
                          <CardTitle className="flex items-center gap-2">
                            <Mail className="h-5 w-5" />
                            Configuration email
                          </CardTitle>
                          <CardDescription>
                            Configurez les paramètres SMTP pour l'envoi d'emails
                          </CardDescription>
                        </CardHeader>
                        <CardContent>
                          <p>Configuration email...</p>
                        </CardContent>
                      </Card>
                    </TabsContent>

                    <TabsContent value="securite">
                      <Card>
                        <CardHeader>
                          <CardTitle className="flex items-center gap-2">
                            <Shield className="h-5 w-5" />
                            Paramètres de sécurité
                          </CardTitle>
                          <CardDescription>
                            Configurez les paramètres de sécurité et d'accès
                          </CardDescription>
                        </CardHeader>
                        <CardContent>
                          <p>Configuration sécurité...</p>
                        </CardContent>
                      </Card>
                    </TabsContent>
                  </Tabs>
                </div>
              </div>
            </div>
          </div>
        </div>
      </SidebarInset>
    </SidebarProvider>
  )
}
