import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import bcrypt from 'bcryptjs'

// Fonction pour déterminer le rôle basé sur l'email
function determineRole(email: string): 'ADMIN' | 'RH' | 'MANAGER' | 'EMPLOYE' {
  const emailLower = email.toLowerCase()
  
  // ADMIN - Emails contenant admin, administrateur ou domaines spéciaux
  if (
    emailLower.includes('admin') ||
    emailLower.includes('administrateur') ||
    emailLower.includes('@admin.') ||
    emailLower.includes('@direction.')
  ) {
    return 'ADMIN'
  }
  
  // RH - Emails contenant rh, hr, ressources humaines
  if (
    emailLower.includes('rh') ||
    emailLower.includes('hr') ||
    emailLower.includes('ressources.humaines') ||
    emailLower.includes('ressourceshumaines')
  ) {
    return 'RH'
  }
  
  // MANAGER - Emails contenant manager, chef, directeur, responsable
  if (
    emailLower.includes('manager') ||
    emailLower.includes('chef') ||
    emailLower.includes('directeur') ||
    emailLower.includes('responsable')
  ) {
    return 'MANAGER'
  }
  
  // Par défaut : EMPLOYE
  return 'EMPLOYE'
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { name, email, password } = body

    // Validation des données
    if (!name || !email || !password) {
      return NextResponse.json(
        { success: false, error: 'Tous les champs sont requis' },
        { status: 400 }
      )
    }

    if (password.length < 6) {
      return NextResponse.json(
        { success: false, error: 'Le mot de passe doit contenir au moins 6 caractères' },
        { status: 400 }
      )
    }

    // Vérifier si l'email existe déjà
    const existingUser = await prisma.user.findUnique({
      where: { email: email.toLowerCase() }
    })

    if (existingUser) {
      return NextResponse.json(
        { success: false, error: 'Un compte avec cet email existe déjà' },
        { status: 400 }
      )
    }

    // Hasher le mot de passe
    const hashedPassword = await bcrypt.hash(password, 12)

    // Déterminer le rôle automatiquement
    const role = determineRole(email)

    // Créer l'utilisateur
    const user = await prisma.user.create({
      data: {
        name: name.trim(),
        email: email.toLowerCase().trim(),
        password: hashedPassword,
        role: role,
        // Champs optionnels avec valeurs par défaut
        poste: role === 'ADMIN' ? 'Administrateur' : 
               role === 'RH' ? 'Responsable RH' :
               role === 'MANAGER' ? 'Manager' : 'Employé',
        departement: role === 'ADMIN' ? 'Direction' :
                     role === 'RH' ? 'Ressources Humaines' :
                     role === 'MANAGER' ? 'Management' : 'Général',
        dateEmbauche: new Date(),
      },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        poste: true,
        departement: true,
        createdAt: true,
      }
    })

    console.log(`✅ Nouvel utilisateur créé: ${user.email} avec le rôle ${user.role}`)

    return NextResponse.json({
      success: true,
      message: 'Compte créé avec succès',
      user: user
    })

  } catch (error) {
    console.error('❌ Erreur lors de l\'inscription:', error)
    
    // Gestion des erreurs Prisma spécifiques
    if (error instanceof Error) {
      if (error.message.includes('Unique constraint')) {
        return NextResponse.json(
          { success: false, error: 'Un compte avec cet email existe déjà' },
          { status: 400 }
        )
      }
    }

    return NextResponse.json(
      { success: false, error: 'Erreur interne du serveur' },
      { status: 500 }
    )
  }
}
