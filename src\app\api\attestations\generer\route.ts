import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { genererAttestationAvecIA, TypeAttestation, DonneesEmploye } from '@/lib/openai';

export async function POST(request: NextRequest) {
  try {
    // Vérifier l'authentification
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Non authentifié' },
        { status: 401 }
      );
    }

    // Parser les données de la requête
    const body = await request.json();
    const { demandeId, typeAttestation, contenuPersonnalise } = body;

    // Validation des données
    if (!demandeId) {
      return NextResponse.json(
        { error: 'ID de demande requis' },
        { status: 400 }
      );
    }

    if (!['travail', 'stage'].includes(typeAttestation)) {
      return NextResponse.json(
        { error: 'Type d\'attestation invalide' },
        { status: 400 }
      );
    }

    // Récupérer la demande avec les informations utilisateur
    const demande = await prisma.demande.findUnique({
      where: { id: demandeId },
      include: {
        user: true,
        attestation: true,
      },
    });

    if (!demande) {
      return NextResponse.json(
        { error: 'Demande non trouvée' },
        { status: 404 }
      );
    }

    // Vérifier que la demande est de type ATTESTATION
    if (demande.type !== 'ATTESTATION') {
      return NextResponse.json(
        { error: 'Cette demande n\'est pas une demande d\'attestation' },
        { status: 400 }
      );
    }

    // Vérifier qu'il n'y a pas déjà une attestation
    if (demande.attestation) {
      return NextResponse.json(
        { error: 'Une attestation existe déjà pour cette demande' },
        { status: 400 }
      );
    }

    let contenuAttestation: string;

    // Si un contenu personnalisé est fourni, l'utiliser
    if (contenuPersonnalise && contenuPersonnalise.trim()) {
      contenuAttestation = contenuPersonnalise.trim();
    } else {
      // Sinon, générer avec l'IA
      const donneesEmploye: DonneesEmploye = {
        nom: demande.user.nom || '',
        prenom: demande.user.prenom || '',
        poste: demande.user.poste || '',
        departement: demande.user.departement || '',
        dateEmbauche: demande.user.dateEmbauche || undefined,
        motif: demande.motifAttestation || undefined,
      };

      // Pour les stages, ajouter les dates spécifiques
      if (typeAttestation === 'stage') {
        donneesEmploye.dateDebutStage = demande.dateDebut || undefined;
        donneesEmploye.dateFinStage = demande.dateFin || undefined;
        donneesEmploye.etablissement = demande.commentaires || undefined; // Utiliser commentaires pour l'établissement
      }

      try {
        contenuAttestation = await genererAttestationAvecIA(
          typeAttestation as TypeAttestation,
          donneesEmploye
        );
      } catch (error) {
        console.error('Erreur génération IA:', error);
        return NextResponse.json(
          { error: 'Erreur lors de la génération avec l\'IA' },
          { status: 500 }
        );
      }
    }

    // Créer l'attestation dans la base de données
    const attestation = await prisma.attestation.create({
      data: {
        contenu: contenuAttestation,
        statut: 'GENEREE',
        genereParIA: !contenuPersonnalise,
        modeleUtilise: contenuPersonnalise ? 'manuel' : `openai-${typeAttestation}`,
        demandeId: demandeId,
      },
      include: {
        demande: {
          include: {
            user: true,
          },
        },
      },
    });

    // Mettre à jour le statut de la demande
    await prisma.demande.update({
      where: { id: demandeId },
      data: {
        statut: 'VALIDEE',
        dateTraitement: new Date(),
      },
    });

    return NextResponse.json({
      success: true,
      attestation: {
        id: attestation.id,
        contenu: attestation.contenu,
        statut: attestation.statut,
        genereParIA: attestation.genereParIA,
        modeleUtilise: attestation.modeleUtilise,
        createdAt: attestation.createdAt,
        demande: {
          id: attestation.demande.id,
          user: {
            name: attestation.demande.user.name,
            nom: attestation.demande.user.nom,
            prenom: attestation.demande.user.prenom,
            poste: attestation.demande.user.poste,
            departement: attestation.demande.user.departement,
          },
        },
      },
    });

  } catch (error) {
    console.error('Erreur lors de la génération d\'attestation:', error);
    return NextResponse.json(
      { error: 'Erreur interne du serveur' },
      { status: 500 }
    );
  }
}

// Route pour tester la connexion OpenAI
export async function GET() {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Non authentifié' },
        { status: 401 }
      );
    }

    // Vérifier si l'utilisateur a les droits (RH ou ADMIN)
    const user = await prisma.user.findUnique({
      where: { email: session.user.email! },
    });

    if (!user || !['RH', 'ADMIN'].includes(user.role)) {
      return NextResponse.json(
        { error: 'Droits insuffisants' },
        { status: 403 }
      );
    }

    // Tester la connexion OpenAI
    const { testerConnexionOpenAI } = await import('@/lib/openai');
    const connexionOK = await testerConnexionOpenAI();

    return NextResponse.json({
      openaiConnected: connexionOK,
      apiKeyConfigured: !!process.env.OPENAI_API_KEY,
    });

  } catch (error) {
    console.error('Erreur lors du test de connexion:', error);
    return NextResponse.json(
      { error: 'Erreur lors du test de connexion' },
      { status: 500 }
    );
  }
}
