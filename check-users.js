const { PrismaClient } = require('@prisma/client')
const bcrypt = require('bcryptjs')

const prisma = new PrismaClient()

async function checkUsers() {
  try {
    console.log('🔍 Vérification des utilisateurs...')
    
    const users = await prisma.user.findMany({
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        password: true
      }
    })
    
    console.log(`📊 Nombre d'utilisateurs trouvés: ${users.length}`)
    
    if (users.length === 0) {
      console.log('❌ Aucun utilisateur trouvé!')
      return
    }
    
    for (const user of users) {
      console.log(`\n👤 ${user.email}`)
      console.log(`   Nom: ${user.name}`)
      console.log(`   Rôle: ${user.role}`)
      console.log(`   Mot de passe hashé: ${user.password ? 'Oui' : 'Non'}`)
      
      if (user.password) {
        // Tester le hash avec les mots de passe connus
        const testPasswords = ['admin123', 'rh123', 'manager123', 'employe123']
        for (const testPassword of testPasswords) {
          const isValid = await bcrypt.compare(testPassword, user.password)
          if (isValid) {
            console.log(`   ✅ Mot de passe valide: ${testPassword}`)
            break
          }
        }
      }
    }
    
  } catch (error) {
    console.error('❌ Erreur:', error)
  } finally {
    await prisma.$disconnect()
  }
}

checkUsers()
