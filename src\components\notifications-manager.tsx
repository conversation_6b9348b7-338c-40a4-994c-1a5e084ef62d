"use client"

import * as React from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { 
  Bell, 
  Clock, 
  CheckCircle, 
  AlertTriangle,
  Send,
  Plus,
  User,
  Calendar,
  Filter,
  MarkAsRead
} from "lucide-react"
import { toast } from "sonner"

interface NotificationsManagerProps {
  notifications: any[]
}

export function NotificationsManager({ notifications }: NotificationsManagerProps) {
  const [filter, setFilter] = React.useState("all")
  const [isCreating, setIsCreating] = React.useState(false)
  const [isLoading, setIsLoading] = React.useState(false)
  const [newNotification, setNewNotification] = React.useState({
    titre: "",
    message: "",
    type: "INFO",
    userId: "",
  })

  // Filtrer les notifications
  const filteredNotifications = notifications.filter(notification => {
    switch (filter) {
      case "unread":
        return !notification.lu
      case "read":
        return notification.lu
      default:
        return true
    }
  })

  // Statistiques
  const stats = {
    total: notifications.length,
    nonLues: notifications.filter(n => !n.lu).length,
    lues: notifications.filter(n => n.lu).length,
    parType: {
      INFO: notifications.filter(n => n.type === 'INFO').length,
      DEMANDE: notifications.filter(n => n.type === 'DEMANDE').length,
      VALIDATION: notifications.filter(n => n.type === 'VALIDATION').length,
      ERREUR: notifications.filter(n => n.type === 'ERREUR').length,
    }
  }

  const getTypeBadge = (type: string) => {
    switch (type) {
      case "INFO":
        return <Badge variant="outline" className="text-blue-600">Info</Badge>
      case "DEMANDE":
        return <Badge variant="outline" className="text-orange-600">Demande</Badge>
      case "VALIDATION":
        return <Badge variant="outline" className="text-green-600">Validation</Badge>
      case "REJET":
        return <Badge variant="outline" className="text-red-600">Rejet</Badge>
      case "ERREUR":
        return <Badge variant="outline" className="text-red-600">Erreur</Badge>
      case "SUCCES":
        return <Badge variant="outline" className="text-green-600">Succès</Badge>
      default:
        return <Badge variant="outline">{type}</Badge>
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "INFO":
        return <Bell className="h-4 w-4 text-blue-500" />
      case "DEMANDE":
        return <Clock className="h-4 w-4 text-orange-500" />
      case "VALIDATION":
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case "REJET":
        return <AlertTriangle className="h-4 w-4 text-red-500" />
      case "ERREUR":
        return <AlertTriangle className="h-4 w-4 text-red-500" />
      case "SUCCES":
        return <CheckCircle className="h-4 w-4 text-green-500" />
      default:
        return <Bell className="h-4 w-4" />
    }
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))
    
    if (diffInHours < 1) {
      return "Il y a moins d'une heure"
    } else if (diffInHours < 24) {
      return `Il y a ${diffInHours} heure${diffInHours > 1 ? 's' : ''}`
    } else if (diffInHours < 48) {
      return "Hier"
    } else {
      return date.toLocaleDateString('fr-FR')
    }
  }

  const handleCreateNotification = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!newNotification.titre || !newNotification.message) {
      toast.error('Titre et message sont obligatoires')
      return
    }

    setIsLoading(true)
    try {
      // Ici vous pourriez appeler une action pour créer la notification
      toast.success('Notification créée avec succès')
      setNewNotification({
        titre: "",
        message: "",
        type: "INFO",
        userId: "",
      })
      setIsCreating(false)
    } catch (error) {
      toast.error('Erreur lors de la création')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* Statistiques */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total</CardTitle>
            <Bell className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
            <p className="text-xs text-muted-foreground">
              notifications au total
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Non lues</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.nonLues}</div>
            <p className="text-xs text-muted-foreground">
              notifications en attente
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Demandes</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.parType.DEMANDE}</div>
            <p className="text-xs text-muted-foreground">
              notifications de demandes
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Validations</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.parType.VALIDATION}</div>
            <p className="text-xs text-muted-foreground">
              notifications de validation
            </p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="liste" className="w-full">
        <TabsList>
          <TabsTrigger value="liste">Toutes les notifications</TabsTrigger>
          <TabsTrigger value="envoyer">Envoyer une notification</TabsTrigger>
        </TabsList>
        
        <TabsContent value="liste">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Notifications récentes</CardTitle>
                  <CardDescription>
                    Gérez vos notifications et alertes système
                  </CardDescription>
                </div>
                <div className="flex gap-2">
                  <Select value={filter} onValueChange={setFilter}>
                    <SelectTrigger className="w-[150px]">
                      <SelectValue placeholder="Filtrer" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">Toutes</SelectItem>
                      <SelectItem value="unread">Non lues</SelectItem>
                      <SelectItem value="read">Lues</SelectItem>
                    </SelectContent>
                  </Select>
                  <Button onClick={() => setIsCreating(true)}>
                    <Plus className="mr-2 h-4 w-4" />
                    Nouvelle
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {filteredNotifications.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    Aucune notification trouvée
                  </div>
                ) : (
                  filteredNotifications.map((notification) => (
                    <div
                      key={notification.id}
                      className={`border rounded-lg p-4 space-y-2 transition-colors hover:bg-muted/50 ${
                        !notification.lu ? 'bg-blue-50 border-blue-200' : 'bg-background'
                      }`}
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex items-start gap-3 flex-1">
                          <div className="mt-1">
                            {getTypeIcon(notification.type)}
                          </div>
                          <div className="flex-1 space-y-1">
                            <div className="flex items-center gap-2">
                              <h4 className="font-medium">{notification.titre}</h4>
                              {getTypeBadge(notification.type)}
                              {!notification.lu && (
                                <Badge variant="default" className="text-xs">Nouveau</Badge>
                              )}
                            </div>
                            <p className="text-sm text-muted-foreground">
                              {notification.message}
                            </p>
                            <div className="flex items-center gap-4 text-xs text-muted-foreground">
                              <span className="flex items-center gap-1">
                                <User className="h-3 w-3" />
                                {notification.user?.name || 'Système'}
                              </span>
                              <span className="flex items-center gap-1">
                                <Calendar className="h-3 w-3" />
                                {formatDate(notification.createdAt)}
                              </span>
                            </div>
                          </div>
                        </div>
                        
                        <div className="flex gap-2">
                          {!notification.lu && (
                            <Button variant="outline" size="sm">
                              <MarkAsRead className="mr-2 h-4 w-4" />
                              Marquer comme lu
                            </Button>
                          )}
                        </div>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="envoyer">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Send className="h-5 w-5" />
                Envoyer une nouvelle notification
              </CardTitle>
              <CardDescription>
                Créez et envoyez une notification à un ou plusieurs utilisateurs
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleCreateNotification} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="titre">Titre *</Label>
                    <Input
                      id="titre"
                      value={newNotification.titre}
                      onChange={(e) => setNewNotification(prev => ({ ...prev, titre: e.target.value }))}
                      placeholder="Titre de la notification"
                      required
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="type">Type</Label>
                    <Select
                      value={newNotification.type}
                      onValueChange={(value) => setNewNotification(prev => ({ ...prev, type: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Sélectionner un type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="INFO">Information</SelectItem>
                        <SelectItem value="DEMANDE">Demande</SelectItem>
                        <SelectItem value="VALIDATION">Validation</SelectItem>
                        <SelectItem value="REJET">Rejet</SelectItem>
                        <SelectItem value="SUCCES">Succès</SelectItem>
                        <SelectItem value="ERREUR">Erreur</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="message">Message *</Label>
                  <Textarea
                    id="message"
                    value={newNotification.message}
                    onChange={(e) => setNewNotification(prev => ({ ...prev, message: e.target.value }))}
                    placeholder="Contenu de la notification..."
                    rows={4}
                    required
                  />
                </div>
                
                <div className="flex gap-2">
                  <Button type="submit" disabled={isLoading}>
                    <Send className="mr-2 h-4 w-4" />
                    {isLoading ? 'Envoi...' : 'Envoyer la notification'}
                  </Button>
                  <Button 
                    type="button" 
                    variant="outline" 
                    onClick={() => {
                      setIsCreating(false)
                      setNewNotification({
                        titre: "",
                        message: "",
                        type: "INFO",
                        userId: "",
                      })
                    }}
                  >
                    Annuler
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
