# 🤖 Guide d'intégration OpenAI pour la génération d'attestations

## 📋 Vue d'ensemble

Votre système RH dispose maintenant d'une intégration complète avec l'API OpenAI pour générer automatiquement des attestations de travail et de stage professionnelles et personnalisées.

## ✨ Fonctionnalités ajoutées

### 🎯 Génération intelligente d'attestations
- **Attestations de travail** : Générées automatiquement avec les informations de l'employé
- **Attestations de stage** : Adaptées aux stagiaires avec dates et établissement
- **Contenu personnalisé** : L'IA adapte le contenu selon le contexte
- **Fallback automatique** : Si l'IA n'est pas disponible, utilise un modèle de base

### 🔧 Interface améliorée
- **Statut en temps réel** : Affichage de l'état de connexion OpenAI
- **Sélection de type** : Choix entre attestation de travail ou de stage
- **Prévisualisation** : Aperçu avant génération
- **Configuration IA** : Page dédiée pour gérer l'intégration

## 🚀 Configuration OpenAI

### 1. Obtenir une clé API OpenAI

1. **Créer un compte OpenAI** :
   - Rendez-vous sur [platform.openai.com](https://platform.openai.com)
   - Créez un compte ou connectez-vous

2. **Générer une clé API** :
   - Allez dans "API Keys" dans votre profil
   - Cliquez sur "Create new secret key"
   - Donnez un nom à votre clé (ex: "RH-Manager-Attestations")
   - Copiez la clé générée (elle commence par `sk-`)

3. **Ajouter des crédits** :
   - OpenAI offre des crédits gratuits pour commencer
   - Ajoutez une méthode de paiement si nécessaire
   - Le coût est très faible : ~$0.001 par attestation

### 2. Configurer la clé API

1. **Modifier le fichier .env.local** :
   ```bash
   # Configuration OpenAI API
   OPENAI_API_KEY="sk-votre-cle-api-ici"
   ```

2. **Redémarrer le serveur** :
   ```bash
   npm run dev
   ```

3. **Vérifier la configuration** :
   - Allez dans "Configuration IA" dans le menu
   - Le statut devrait afficher "✅ IA OpenAI connectée et prête"

## 💰 Coûts et utilisation

### Tarification OpenAI
- **Modèle utilisé** : gpt-3.5-turbo (le plus économique)
- **Coût** : $0.002 par 1000 tokens
- **Attestation typique** : 200-300 tokens
- **Coût par attestation** : ~$0.0006 (moins d'un centime)

### Crédits gratuits
- OpenAI offre $5 de crédits gratuits
- Cela représente environ 8000+ attestations gratuites
- Parfait pour tester et commencer

## 🎨 Utilisation

### Générer une attestation

1. **Aller dans "Attestations"** dans le menu principal
2. **Onglet "Générer"** :
   - Sélectionner une demande d'attestation
   - Choisir le type (Travail ou Stage)
   - Laisser vide pour génération automatique OU écrire un contenu personnalisé
   - Cliquer sur "Générer avec IA"

3. **Résultat** :
   - L'IA génère une attestation professionnelle
   - Le document est automatiquement sauvegardé
   - Badge "IA" pour identifier les attestations générées automatiquement

### Types d'attestations

#### 🏢 Attestation de travail
- Certifie l'emploi d'une personne
- Inclut poste, département, date d'embauche
- Adaptée pour démarches administratives

#### 🎓 Attestation de stage
- Certifie la réalisation d'un stage
- Inclut dates, mission, établissement d'origine
- Adaptée pour validation académique

## 🔍 Fonctionnalités avancées

### Prévisualisation
- Voir l'attestation avant génération
- Possibilité de modifier avant validation
- Aperçu formaté professionnel

### Statut en temps réel
- Indicateur de connexion OpenAI
- Messages d'erreur explicites
- Test de connexion disponible

### Fallback automatique
- Si OpenAI n'est pas disponible, utilise un modèle de base
- Aucune interruption de service
- Notification claire du mode utilisé

## 🛠️ Dépannage

### Problèmes courants

#### ❌ "Clé API non configurée"
- Vérifiez que `OPENAI_API_KEY` est dans .env.local
- Redémarrez le serveur après modification
- La clé doit commencer par `sk-`

#### ⚠️ "Connexion échouée"
- Vérifiez votre connexion internet
- Vérifiez que la clé API est valide
- Vérifiez que vous avez des crédits OpenAI

#### 💳 "Quota dépassé"
- Ajoutez des crédits à votre compte OpenAI
- Vérifiez vos limites de taux
- Le système basculera automatiquement en mode manuel

### Tests et diagnostic

1. **Page Configuration IA** :
   - Statut de connexion en temps réel
   - Test de connexion manuel
   - Génération d'exemples

2. **Logs du serveur** :
   - Erreurs détaillées dans la console
   - Messages de debug pour l'API OpenAI

## 📈 Avantages de l'IA

### Pour les RH
- **Gain de temps** : Génération instantanée
- **Qualité** : Texte professionnel et adapté
- **Cohérence** : Format standardisé
- **Personnalisation** : Contenu adapté au contexte

### Pour l'entreprise
- **Efficacité** : Traitement rapide des demandes
- **Professionnalisme** : Documents de qualité
- **Traçabilité** : Historique des générations
- **Évolutivité** : Facilement extensible

## 🔮 Évolutions futures

### Fonctionnalités prévues
- **Modèles personnalisés** : Templates spécifiques à l'entreprise
- **Langues multiples** : Génération en plusieurs langues
- **Signatures automatiques** : Intégration de signatures numériques
- **Export PDF** : Génération directe en PDF
- **Envoi automatique** : Email automatique aux employés

### Intégrations possibles
- **Autres modèles IA** : Support de Claude, Gemini, etc.
- **OCR** : Reconnaissance de documents existants
- **Workflow** : Approbation automatique selon les règles
- **Analytics** : Statistiques d'utilisation de l'IA

## 📞 Support

### En cas de problème
1. Vérifiez ce guide
2. Consultez la page "Configuration IA"
3. Vérifiez les logs du serveur
4. Testez avec les exemples fournis

### Ressources
- [Documentation OpenAI](https://platform.openai.com/docs)
- [Tarification OpenAI](https://openai.com/pricing)
- [Statut des services OpenAI](https://status.openai.com)

---

🎉 **Félicitations !** Votre système RH dispose maintenant d'une IA avancée pour la génération d'attestations. L'intégration est transparente et améliore considérablement l'efficacité de vos processus RH.
