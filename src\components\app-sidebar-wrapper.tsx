import { AppSidebar } from "@/components/app-sidebar"
import { getCurrentUser, hasPermission } from "@/lib/auth-utils"
import { redirect } from "next/navigation"

export async function AppSidebarWrapper({ ...props }: React.ComponentProps<typeof AppSidebar>) {
  const user = await getCurrentUser()

  if (!user) {
    redirect("/auth/signin")
  }

  // Filtrer les éléments de navigation selon les permissions
  const navMain = [
    {
      title: "Dashboard",
      url: "/dashboard",
      icon: "IconDashboard",
    },
    {
      title: "Demandes",
      url: "/dashboard/demandes",
      icon: "IconListDetails",
    },
    {
      title: "Attestations",
      url: "/dashboard/attestations",
      icon: "IconFileDescription",
    },
    // Employés - seulement pour RH et ADMIN
    ...(hasPermission(user.role, "view_all_profiles") ? [{
      title: "Employés",
      url: "/dashboard/employes",
      icon: "IconUsers",
    }] : []),
    // Paramètres - seulement pour ADMIN
    ...(user.role === "ADMIN" ? [{
      title: "Test CRUD",
      url: "/dashboard/test-crud",
      icon: "IconDatabase",
    }] : []),
  ]

  const navSecondary = [
    // Paramètres - seulement pour ADMIN
    ...(user.role === "ADMIN" ? [{
      title: "Paramètres",
      url: "/dashboard/parametres",
      icon: "IconSettings",
    }] : []),
    {
      title: "Notifications",
      url: "/dashboard/notifications",
      icon: "IconHelp",
    },
  ]

  const userData = {
    name: user.name || user.email,
    email: user.email,
    avatar: user.image || "/avatars/default.jpg",
    role: user.role,
  }

  return <AppSidebar user={userData} navMain={navMain} navSecondary={navSecondary} {...props} />
}
