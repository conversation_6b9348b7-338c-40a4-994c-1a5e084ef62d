const { PrismaClient } = require('@prisma/client')
const bcrypt = require('bcryptjs')

const prisma = new PrismaClient()

async function testAuth() {
  try {
    console.log('🔐 Test d\'authentification...')
    
    const testCredentials = [
      { email: '<EMAIL>', password: 'admin123' },
      { email: '<EMAIL>', password: 'rh123' },
      { email: '<EMAIL>', password: 'manager123' },
      { email: '<EMAIL>', password: 'employe123' }
    ]
    
    for (const cred of testCredentials) {
      console.log(`\n🧪 Test: ${cred.email}`)
      
      const user = await prisma.user.findUnique({
        where: { email: cred.email }
      })
      
      if (!user) {
        console.log('❌ Utilisateur non trouvé')
        continue
      }
      
      if (!user.password) {
        console.log('❌ Pas de mot de passe défini')
        continue
      }
      
      const isValid = await bcrypt.compare(cred.password, user.password)
      
      if (isValid) {
        console.log('✅ Authentification réussie')
        console.log(`   ID: ${user.id}`)
        console.log(`   Nom: ${user.name}`)
        console.log(`   Rôle: ${user.role}`)
      } else {
        console.log('❌ Mot de passe incorrect')
      }
    }
    
  } catch (error) {
    console.error('❌ Erreur:', error)
  } finally {
    await prisma.$disconnect()
  }
}

testAuth()
