"use client"

import * as React from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { 
  Bot, 
  CheckCircle, 
  XCircle, 
  AlertCircle,
  Key,
  TestTube,
  Settings,
  Sparkles,
  FileText,
  Building,
  User
} from "lucide-react"
import { toast } from "sonner"

export function ConfigurationIA() {
  const [apiKey, setApiKey] = React.useState("")
  const [isTestingConnection, setIsTestingConnection] = React.useState(false)
  const [connectionStatus, setConnectionStatus] = React.useState<{
    connected: boolean;
    configured: boolean;
    loading: boolean;
    lastTest?: Date;
  }>({ connected: false, configured: false, loading: true })

  // Vérifier le statut au chargement
  React.useEffect(() => {
    verifierStatutOpenAI()
  }, [])

  const verifierStatutOpenAI = async () => {
    try {
      const response = await fetch('/api/attestations/generer')
      if (response.ok) {
        const data = await response.json()
        setConnectionStatus({
          connected: data.openaiConnected,
          configured: data.apiKeyConfigured,
          loading: false,
          lastTest: new Date()
        })
      } else {
        setConnectionStatus({
          connected: false,
          configured: false,
          loading: false
        })
      }
    } catch (error) {
      console.error('Erreur vérification OpenAI:', error)
      setConnectionStatus({
        connected: false,
        configured: false,
        loading: false
      })
    }
  }

  const testerConnexion = async () => {
    setIsTestingConnection(true)
    try {
      await verifierStatutOpenAI()
      if (connectionStatus.connected) {
        toast.success("Connexion OpenAI réussie !")
      } else {
        toast.error("Échec de la connexion OpenAI")
      }
    } catch (error) {
      toast.error("Erreur lors du test de connexion")
    } finally {
      setIsTestingConnection(false)
    }
  }

  const genererExemple = async (type: 'travail' | 'stage') => {
    try {
      const donneesTest = {
        travail: {
          nom: "Dupont",
          prenom: "Marie",
          poste: "Développeuse Full Stack",
          departement: "Informatique",
          dateEmbauche: "2022-01-15",
          motif: "demande de prêt bancaire"
        },
        stage: {
          nom: "Martin",
          prenom: "Pierre",
          poste: "Stagiaire Développeur",
          departement: "Informatique",
          dateDebutStage: "2024-01-15",
          dateFinStage: "2024-06-15",
          etablissement: "Université Paris-Saclay",
          motif: "validation de stage"
        }
      }

      // Simuler un appel à l'API (en réalité, il faudrait créer une route de test)
      toast.info(`Génération d'exemple d'attestation de ${type} en cours...`)
      
      // Ici, vous pourriez ajouter un appel réel à votre API pour tester
      setTimeout(() => {
        toast.success(`Exemple d'attestation de ${type} généré avec succès !`)
      }, 2000)

    } catch (error) {
      toast.error("Erreur lors de la génération d'exemple")
    }
  }

  return (
    <div className="space-y-6">
      {/* Statut de connexion */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bot className="h-5 w-5" />
            Statut de l'IA OpenAI
          </CardTitle>
          <CardDescription>
            État actuel de la connexion à l'API OpenAI
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {connectionStatus.loading ? (
            <div className="flex items-center gap-2">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-900"></div>
              <span>Vérification en cours...</span>
            </div>
          ) : (
            <Alert className={connectionStatus.connected ? "border-green-200 bg-green-50" : "border-orange-200 bg-orange-50"}>
              <div className="flex items-center gap-2">
                {connectionStatus.connected ? (
                  <CheckCircle className="h-4 w-4 text-green-600" />
                ) : connectionStatus.configured ? (
                  <AlertCircle className="h-4 w-4 text-orange-600" />
                ) : (
                  <XCircle className="h-4 w-4 text-red-600" />
                )}
                <AlertDescription className={connectionStatus.connected ? "text-green-800" : "text-orange-800"}>
                  {connectionStatus.connected 
                    ? "✅ IA OpenAI connectée et opérationnelle"
                    : connectionStatus.configured 
                      ? "⚠️ Clé API configurée mais connexion échouée"
                      : "❌ Clé API OpenAI non configurée"
                  }
                </AlertDescription>
              </div>
            </Alert>
          )}

          <div className="flex gap-2">
            <Button 
              onClick={testerConnexion} 
              disabled={isTestingConnection}
              variant="outline"
            >
              <TestTube className="mr-2 h-4 w-4" />
              {isTestingConnection ? "Test en cours..." : "Tester la connexion"}
            </Button>
            {connectionStatus.lastTest && (
              <span className="text-sm text-muted-foreground self-center">
                Dernier test: {connectionStatus.lastTest.toLocaleTimeString()}
              </span>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Configuration */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Configuration OpenAI
          </CardTitle>
          <CardDescription>
            Configurez votre clé API OpenAI pour activer la génération automatique
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Alert>
            <Key className="h-4 w-4" />
            <AlertDescription>
              <strong>Comment obtenir votre clé API OpenAI :</strong>
              <ol className="list-decimal list-inside mt-2 space-y-1">
                <li>Rendez-vous sur <a href="https://platform.openai.com" target="_blank" className="text-blue-600 underline">platform.openai.com</a></li>
                <li>Créez un compte ou connectez-vous</li>
                <li>Allez dans "API Keys" dans votre profil</li>
                <li>Cliquez sur "Create new secret key"</li>
                <li>Copiez la clé et ajoutez-la dans votre fichier .env.local</li>
              </ol>
            </AlertDescription>
          </Alert>

          <div className="space-y-2">
            <Label htmlFor="apikey">Clé API OpenAI</Label>
            <Input
              id="apikey"
              type="password"
              placeholder="sk-..."
              value={apiKey}
              onChange={(e) => setApiKey(e.target.value)}
            />
            <p className="text-xs text-muted-foreground">
              La clé API doit être configurée dans le fichier .env.local sur le serveur
            </p>
          </div>

          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              <strong>Coûts OpenAI :</strong> L'API OpenAI est payante après les crédits gratuits. 
              Le modèle gpt-3.5-turbo coûte environ $0.002 par 1000 tokens. 
              Une attestation typique utilise ~200-300 tokens, soit moins de $0.001 par attestation.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>

      {/* Tests et exemples */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Sparkles className="h-5 w-5" />
            Tests et exemples
          </CardTitle>
          <CardDescription>
            Testez la génération d'attestations avec des données d'exemple
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card className="border-dashed">
              <CardContent className="pt-6">
                <div className="flex flex-col items-center text-center space-y-2">
                  <Building className="h-8 w-8 text-blue-600" />
                  <h3 className="font-medium">Attestation de travail</h3>
                  <p className="text-sm text-muted-foreground">
                    Teste la génération pour un employé
                  </p>
                  <Button 
                    onClick={() => genererExemple('travail')}
                    disabled={!connectionStatus.connected}
                    size="sm"
                  >
                    <FileText className="mr-2 h-4 w-4" />
                    Générer exemple
                  </Button>
                </div>
              </CardContent>
            </Card>

            <Card className="border-dashed">
              <CardContent className="pt-6">
                <div className="flex flex-col items-center text-center space-y-2">
                  <User className="h-8 w-8 text-purple-600" />
                  <h3 className="font-medium">Attestation de stage</h3>
                  <p className="text-sm text-muted-foreground">
                    Teste la génération pour un stagiaire
                  </p>
                  <Button 
                    onClick={() => genererExemple('stage')}
                    disabled={!connectionStatus.connected}
                    size="sm"
                  >
                    <FileText className="mr-2 h-4 w-4" />
                    Générer exemple
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>

          {!connectionStatus.connected && (
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                Configurez d'abord votre clé API OpenAI pour tester la génération d'exemples.
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
