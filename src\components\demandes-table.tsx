"use client"

import * as React from "react"
import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table"
import { ArrowUpDown, MoreHorizontal, Filter } from "lucide-react"

import { Button } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Input } from "@/components/ui/input"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { updateDemandeStatus, deleteDemande } from "@/lib/actions"
import { toast } from "sonner"

export type Demande = {
  id: string
  type: string
  message: string
  status: string
  createdAt: string
  user: {
    id: string
    name: string
    email: string
    departement?: string
    poste?: string
  }
  attestation?: any
}

const getStatutBadge = (status: string) => {
  switch (status) {
    case "pending":
      return <Badge variant="outline" className="text-orange-600">En attente</Badge>
    case "approved":
      return <Badge variant="outline" className="text-green-600">Approuvée</Badge>
    case "rejected":
      return <Badge variant="outline" className="text-red-600">Rejetée</Badge>
    case "in_progress":
      return <Badge variant="outline" className="text-blue-600">En cours</Badge>
    default:
      return <Badge variant="outline">{status}</Badge>
  }
}

const getTypeBadge = (type: string) => {
  switch (type) {
    case "CONGE":
      return <Badge variant="secondary">Congé</Badge>
    case "MATERIEL":
      return <Badge variant="secondary">Matériel</Badge>
    case "ATTESTATION":
      return <Badge variant="secondary">Attestation</Badge>
    default:
      return <Badge variant="secondary">{type}</Badge>
  }
}

// Fonction pour créer les colonnes avec les actions
const createColumns = (
  handleApprove: (id: string) => void,
  handleReject: (id: string) => void,
  handleDelete: (id: string) => void,
  isLoading: boolean
): ColumnDef<Demande>[] => [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },

  {
    id: "employe",
    accessorFn: (row) => row.user?.name || 'Utilisateur inconnu',
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Employé
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      )
    },
    cell: ({ row }) => (
      <div className="flex flex-col">
        <div className="font-medium">{row.original.user?.name || 'Utilisateur inconnu'}</div>
        <div className="text-sm text-muted-foreground">{row.original.user?.email}</div>
      </div>
    ),
  },
  {
    id: "departement",
    accessorFn: (row) => row.user?.departement || 'Non défini',
    header: "Département",
    cell: ({ row }) => (
      <div className="flex flex-col">
        <div>{row.original.user?.departement || 'Non défini'}</div>
        <div className="text-sm text-muted-foreground">{row.original.user?.poste || 'Non défini'}</div>
      </div>
    ),
  },
  {
    accessorKey: "type",
    header: "Type",
    cell: ({ row }) => getTypeBadge(row.getValue("type")),
  },
  {
    accessorKey: "status",
    header: "Statut",
    cell: ({ row }) => getStatutBadge(row.getValue("status")),
  },
  {
    accessorKey: "createdAt",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Date
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      )
    },
    cell: ({ row }) => {
      const date = new Date(row.getValue("createdAt"))
      return <div>{date.toLocaleDateString("fr-FR")}</div>
    },
  },
  {
    id: "actions",
    enableHiding: false,
    cell: ({ row }) => {
      const demande = row.original

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <span className="sr-only">Open menu</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            <DropdownMenuItem>Voir détails</DropdownMenuItem>
            <DropdownMenuItem>Modifier</DropdownMenuItem>
            {demande.status === "pending" && (
              <>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  className="text-green-600"
                  onClick={() => handleApprove(demande.id)}
                  disabled={isLoading}
                >
                  Approuver
                </DropdownMenuItem>
                <DropdownMenuItem
                  className="text-red-600"
                  onClick={() => handleReject(demande.id)}
                  disabled={isLoading}
                >
                  Rejeter
                </DropdownMenuItem>
              </>
            )}
            <DropdownMenuSeparator />
            <DropdownMenuItem
              className="text-red-600"
              onClick={() => handleDelete(demande.id)}
              disabled={isLoading}
            >
              Supprimer
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      )
    },
  },
]

interface DemandesTableProps {
  data: Demande[]
}

export function DemandesTable({ data }: DemandesTableProps) {
  const [sorting, setSorting] = React.useState<SortingState>([])
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([])
  const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>({})
  const [rowSelection, setRowSelection] = React.useState({})
  const [isLoading, setIsLoading] = React.useState(false)

  // Fonctions pour gérer les actions CRUD
  const handleApprove = async (demandeId: string) => {
    setIsLoading(true)
    try {
      const result = await updateDemandeStatus(demandeId, 'approved')
      if (result.success) {
        toast.success('Demande approuvée avec succès')
      } else {
        toast.error(result.error || 'Erreur lors de l\'approbation')
      }
    } catch (error) {
      toast.error('Erreur lors de l\'approbation')
    } finally {
      setIsLoading(false)
    }
  }

  const handleReject = async (demandeId: string) => {
    setIsLoading(true)
    try {
      const result = await updateDemandeStatus(demandeId, 'rejected')
      if (result.success) {
        toast.success('Demande rejetée')
      } else {
        toast.error(result.error || 'Erreur lors du rejet')
      }
    } catch (error) {
      toast.error('Erreur lors du rejet')
    } finally {
      setIsLoading(false)
    }
  }

  const handleDelete = async (demandeId: string) => {
    if (!confirm('Êtes-vous sûr de vouloir supprimer cette demande ?')) {
      return
    }

    setIsLoading(true)
    try {
      const result = await deleteDemande(demandeId)
      if (result.success) {
        toast.success('Demande supprimée avec succès')
      } else {
        toast.error(result.error || 'Erreur lors de la suppression')
      }
    } catch (error) {
      toast.error('Erreur lors de la suppression')
    } finally {
      setIsLoading(false)
    }
  }

  // Créer les colonnes avec les fonctions d'action
  const columns = React.useMemo(
    () => createColumns(handleApprove, handleReject, handleDelete, isLoading),
    [isLoading]
  )

  const table = useReactTable({
    data,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
    },
  })

  return (
    <Card>
      <CardHeader>
        <CardTitle>Liste des Demandes</CardTitle>
        <CardDescription>
          Gérez et suivez toutes les demandes des employés
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="w-full">
          <div className="flex items-center py-4">
            <Input
              placeholder="Filtrer par employé..."
              value={(table.getColumn("employe")?.getFilterValue() as string) ?? ""}
              onChange={(event) =>
                table.getColumn("employe")?.setFilterValue(event.target.value)
              }
              className="max-w-sm"
            />
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" className="ml-auto">
                  Colonnes <Filter className="ml-2 h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                {table
                  .getAllColumns()
                  .filter((column) => column.getCanHide())
                  .map((column) => {
                    return (
                      <DropdownMenuCheckboxItem
                        key={column.id}
                        className="capitalize"
                        checked={column.getIsVisible()}
                        onCheckedChange={(value) =>
                          column.toggleVisibility(!!value)
                        }
                      >
                        {column.id}
                      </DropdownMenuCheckboxItem>
                    )
                  })}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                {table.getHeaderGroups().map((headerGroup) => (
                  <TableRow key={headerGroup.id}>
                    {headerGroup.headers.map((header) => {
                      return (
                        <TableHead key={header.id}>
                          {header.isPlaceholder
                            ? null
                            : flexRender(
                                header.column.columnDef.header,
                                header.getContext()
                              )}
                        </TableHead>
                      )
                    })}
                  </TableRow>
                ))}
              </TableHeader>
              <TableBody>
                {table.getRowModel().rows?.length ? (
                  table.getRowModel().rows.map((row) => (
                    <TableRow
                      key={row.id}
                      data-state={row.getIsSelected() && "selected"}
                    >
                      {row.getVisibleCells().map((cell) => (
                        <TableCell key={cell.id}>
                          {flexRender(
                            cell.column.columnDef.cell,
                            cell.getContext()
                          )}
                        </TableCell>
                      ))}
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell
                      colSpan={columns.length}
                      className="h-24 text-center"
                    >
                      Aucune demande trouvée.
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
          <div className="flex items-center justify-end space-x-2 py-4">
            <div className="flex-1 text-sm text-muted-foreground">
              {table.getFilteredSelectedRowModel().rows.length} sur{" "}
              {table.getFilteredRowModel().rows.length} ligne(s) sélectionnée(s).
            </div>
            <div className="space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => table.previousPage()}
                disabled={!table.getCanPreviousPage()}
              >
                Précédent
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => table.nextPage()}
                disabled={!table.getCanNextPage()}
              >
                Suivant
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
